# Qiankun ES6 模块错误解决方案

## 问题描述

在使用 qiankun 微前端框架时，开发环境中可能会遇到以下错误：

```
Uncaught SyntaxError: Cannot use import statement outside a module
```

这个错误通常发生在：
1. 使用 Vite 作为构建工具的开发环境
2. qiankun 尝试加载包含 ES6 模块语法的脚本
3. React Refresh 或其他开发工具的热更新脚本

## 解决方案

### 1. qiankun 配置优化

在 `src/config/microAppConfig.js` 中，我们对 qiankun 配置进行了以下优化：

#### 沙箱配置
```javascript
sandbox: {
  strictStyleIsolation: false,
  experimentalStyleIsolation: false,
  // 解决 ES6 模块语法问题
  loose: true,
  // 允许子应用访问更多全局变量
  speedy: false,
}
```

#### 模板处理
```javascript
getTemplate: (tpl) => {
  // 移除可能导致问题的 Vite 相关脚本
  let processedTpl = tpl;
  
  // 移除 React Refresh 相关脚本
  processedTpl = processedTpl.replace(
    /<script type="module"[^>]*>[\s\S]*?import RefreshRuntime[\s\S]*?<\/script>/gi,
    ''
  );
  
  // 移除 @react-refresh 相关脚本
  processedTpl = processedTpl.replace(
    /<script type="module"[^>]*>[\s\S]*?@react-refresh[\s\S]*?<\/script>/gi,
    ''
  );
  
  // 移除 Vite 客户端脚本
  processedTpl = processedTpl.replace(
    /<script type="module"[^>]*src="[^"]*@vite\/client[^"]*"[^>]*><\/script>/gi,
    ''
  );
  
  return processedTpl;
}
```

#### 资源过滤
```javascript
excludeAssetFilter: (assetUrl) => {
  const excludePatterns = [
    '@react-refresh',
    '/@vite/',
    'vite/dist/client',
    '@vite/client',
    'vite/modulepreload-polyfill'
  ];
  
  return excludePatterns.some(pattern => assetUrl.includes(pattern));
}
```

### 2. 全局错误处理

在 `src/App.jsx` 中添加了专门的错误处理器：

```javascript
const handleQiankunError = (event) => {
  const error = event.error || event.reason;
  if (error && error.message && error.message.includes('Cannot use import statement outside a module')) {
    console.warn('⚠️ 检测到 qiankun ES6 模块错误，这是开发环境的已知问题');
    console.log('💡 错误已被捕获，应用将继续正常运行');
    event.preventDefault(); // 阻止错误传播
    return;
  }
};

// 监听未捕获的错误
window.addEventListener('error', handleQiankunError);
window.addEventListener('unhandledrejection', handleQiankunError);
```

### 3. 控制台错误过滤

创建了 `initQiankunErrorHandling` 函数来过滤开发环境中的噪音错误：

```javascript
export const initQiankunErrorHandling = () => {
  const originalConsoleError = console.error;
  
  console.error = (...args) => {
    const errorMessage = args.join(' ');
    
    // 检查是否是 ES6 模块错误
    if (errorMessage.includes('Cannot use import statement outside a module') ||
        errorMessage.includes('import-html-entry') ||
        errorMessage.includes('@react-refresh')) {
      
      console.warn('⚠️ qiankun ES6 模块错误已被捕获:', errorMessage);
      console.log('💡 这是开发环境的已知问题，不会影响应用功能');
      return; // 不输出错误信息
    }
    
    // 其他错误正常输出
    originalConsoleError.apply(console, args);
  };
};
```

## 使用说明

1. **开发环境**: 这些配置主要针对开发环境中的问题，不会影响生产环境的正常运行。

2. **子应用配置**: 确保子应用也正确配置了 qiankun 的生命周期函数。

3. **错误监控**: 在生产环境中，建议使用专业的错误监控工具来跟踪真正的错误。

## 注意事项

1. 这个解决方案主要针对开发环境中的 Vite + React + qiankun 组合。
2. 如果子应用使用不同的构建工具，可能需要调整配置。
3. 定期更新 qiankun 版本，新版本可能已经修复了这些问题。

## 验证方法

修改后，应用应该能够：
1. 正常启动主应用
2. 成功加载子应用
3. 不再在控制台显示 ES6 模块相关的错误
4. 微前端功能正常工作

如果仍然遇到问题，请检查：
1. 子应用是否正常运行
2. 网络连接是否正常
3. 环境变量配置是否正确
