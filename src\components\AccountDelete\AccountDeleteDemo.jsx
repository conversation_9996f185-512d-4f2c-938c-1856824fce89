import React, { useState } from "react";
import { Box, Button, Typography, Paper, Alert } from "@mui/material";
import { useTranslation } from "react-i18next";
import CloseAccount from "@/layout/components/CloseAccount";

const AccountDeleteDemo = () => {
  const { t, i18n } = useTranslation();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [notification, setNotification] = useState({
    show: false,
    type: "",
    message: "",
  });

  // 模拟用户数据
  const userData = {
    name: "测试用户",
    email: "<EMAIL>",
    accountType: "Premium",
    createdDate: "2024-01-15",
  };

  // 模拟验证当前密码的函数
  const handleDeleteAccount = async (password) => {
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // 简单验证逻辑（实际应用中应该调用后端API）
    if (password === "123456") {
      setNotification({
        show: true,
        type: "success",
        message: "账户删除成功！",
      });
      setDialogOpen(false);
      return true;
    } else {
      throw new Error("密码不正确，请重新输入");
    }
  };

  const switchLanguage = (lang) => {
    i18n.changeLanguage(lang);
  };

  const clearNotification = () => {
    setNotification({ show: false, type: "", message: "" });
  };

  return (
    <Box sx={{ p: 3, maxWidth: 800, mx: "auto" }}>
      <Typography variant="h4" gutterBottom>
        删除账户对话框演示
      </Typography>

      {/* 语言切换按钮 */}
      <Box sx={{ mb: 3 }}>
        <Button 
          variant="outlined" 
          onClick={() => switchLanguage("zh")}
          sx={{ mr: 1 }}
        >
          中文
        </Button>
        <Button 
          variant="outlined" 
          onClick={() => switchLanguage("en")}
        >
          English
        </Button>
      </Box>

      {/* 通知信息 */}
      {notification.show && (
        <Alert 
          severity={notification.type} 
          onClose={clearNotification}
          sx={{ mb: 3 }}
        >
          {notification.message}
        </Alert>
      )}

      {/* 用户信息卡片 */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          当前账户信息
        </Typography>
        <Typography variant="body2" sx={{ mb: 1 }}>
          <strong>用户名:</strong> {userData.name}
        </Typography>
        <Typography variant="body2" sx={{ mb: 1 }}>
          <strong>邮箱:</strong> {userData.email}
        </Typography>
        <Typography variant="body2" sx={{ mb: 1 }}>
          <strong>账户类型:</strong> {userData.accountType}
        </Typography>
        <Typography variant="body2">
          <strong>创建日期:</strong> {userData.createdDate}
        </Typography>
      </Paper>

      {/* 危险操作区域 */}
      <Paper sx={{ p: 3, border: "1px solid #f44336", backgroundColor: "#fff5f5" }}>
        <Typography variant="h6" color="error" gutterBottom>
          危险操作
        </Typography>
        <Typography variant="body2" sx={{ mb: 2, color: "#666" }}>
          删除账户是不可逆的操作，将永久删除您的所有数据。
        </Typography>
        <Button
          variant="contained"
          color="error"
          onClick={() => setDialogOpen(true)}
          sx={{
            textTransform: "none",
          }}
        >
          删除账户
        </Button>
      </Paper>

      {/* 使用说明 */}
      <Box sx={{ mt: 4 }}>
        <Typography variant="h6" gutterBottom>
          使用说明
        </Typography>
        <Typography variant="body2" sx={{ mb: 2 }}>
          这个演示展示了删除账户确认对话框的功能：
        </Typography>
        <Box component="ul" sx={{ pl: 2 }}>
          <Typography component="li" variant="body2" sx={{ mb: 1 }}>
            点击"删除账户"按钮打开确认对话框
          </Typography>
          <Typography component="li" variant="body2" sx={{ mb: 1 }}>
            对话框包含警告信息和密码确认
          </Typography>
          <Typography component="li" variant="body2" sx={{ mb: 1 }}>
            密码输入框支持显示/隐藏切换
          </Typography>
          <Typography component="li" variant="body2" sx={{ mb: 1 }}>
            支持中英文国际化切换
          </Typography>
          <Typography component="li" variant="body2" sx={{ mb: 1 }}>
            测试密码：<code>123456</code>
          </Typography>
          <Typography component="li" variant="body2">
            输入错误密码会显示错误信息
          </Typography>
        </Box>
      </Box>

      {/* 删除账户对话框 */}
      <CloseAccount
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        onConfirm={handleDeleteAccount}
      />
    </Box>
  );
};

export default AccountDeleteDemo;
