import { private_agree_text, user_agree_text } from "./data";

const en = {
  //公用国际化
  common: {
    common_area_name_regTip:
      "Region names can only start with letters, Chinese characters, and can only contain letters, numbers, Chinese spaces, and underscores",
    name_max: "The length of the name must not exceed 50 words",
    common_login_tips_have_account: "Already have an account?",
    common_login_tips_dont_have_account: `Don't have an account?`,
    common_tabs_account_login: "Account <PERSON><PERSON>",
    common_tabs_quick_login: "Quick Login",
    common_noResults_found: "No matching data",
    common_piece: "Piece",
    common_login: "Login",
    common_register: "Sign up",
    common_login_speech: "Make Information Publishing Easier",
    common_ZKBioMedia: "ZKBio Media",
    common_lonLat: "Latitude and Longitude",
    common_latitude: "Latitude",
    common_longitude: "Longitude",
    common_osVersion: "System Version",
    common_macAddress: "MAC Address",
    common_switcher_timing: "Scheduled Working Duration",
    common_switcher_shutdown: "Scheduled Shutdown",
    common_switcher_startTiming: "Starting Time",
    common_switcher_shutTiming: "Shutdown Time",
    common_select_startTiming: "Starting Time",
    common_select_shutTiming: "Please select the shutdown time",
    common_downloading: "Downloading",
    common_downloaded: "Downloaded",
    common_program: "Program",
    common_template: "Template",
    common_scheduling: "Scheduling",
    common_custom: "Customization",
    common_top: "Top",
    common_bottom: "Bottom",
    common_speed: "Speed",
    common_slow: "Slow",
    common_general: "Moderate",
    common_fast: "Fast",
    common_faster: "Rapid",
    common_direction: "Direction",
    common_up: "Up",
    common_down: "Down",
    common_left: "Left",
    common_right: "Right",
    common_left_to_right: "From left to right",
    common_right_to_left: "From right to left",
    common_expired: "Expired",
    common_more: "More",
    common_enable_group: "Enable Grouping",
    common_stop_group: "Disable Grouping",
    common_clear_program: "Clear Program",
    common_auth: "Authenticate",
    common_play_control: "Play Control",
    common_screen_control: "Screen Control",
    common_volume_control: "Volume Control",
    common_volume_mute: "Mute",
    common_volume_set: "Volume Settings",
    common_power_control: "Power Control",
    common_device_reboot: "Reboot Device",
    common_device_timing: "Device Timing",
    common_device_playStart: "Start Playing",
    common_device_playStop: "Stop Playing",
    common_device_appUpgrade: "Upgrade Application",
    common_device_modeSet: "Mode",
    common_device_backlightSet: "Backlight",
    common_device_brightnessSet: "Brightness",
    common_device_rotateSet: "Rotation",
    common_device_HAVSet: "Horizontal or Vertical",
    common_device_rotation: "Screen Rotation",
    common_device_rotationDegree: "Rotational Angle",
    common_device_modeSD: "SD Card ",
    common_device_modeCloud: "Server Mode",
    common_prompt_title: "Reminder",
    common_prompt_warning: "Warning",
    common_prompt_error: "Error",
    common_prompt_noData: "No Data",
    common_param_width: "Width",
    common_param_height: "Height",
    common_upload_success: "Successfully Uploaded!",
    common_upload_failed: "Failed to Upload!",
    common_upload_image_message: "Please upload an image less than 10MB",
    common_upload_material_message: "Please upload material less than 1GB",
    common_upload_message: "Upload Files",
    common_upload_dept_message: "Please choose, the agency is being imported",
    common_add_success: "Successfully Added!",
    common_add_failed: "Failed to Add",
    common_edit_success: "Successfully Edited!",
    common_edit_failed: "Failed to Edit!",
    common_del_success: "Deleted",
    common_del_failed: "Failed to Delete!",
    common_save_success: "Saved",
    common_save_failed: "Failed to Save!",
    common_auth_success: "Verified",
    common_edit_group: "Grouping",
    common_edit_save: "Save",
    common_edit_ok: "OK",
    common_edit_cancel: "Cancel",
    common_verification: "Accepted",
    common_deny: "Denied",
    common_none: "None",
    common_op_success: "Successful",
    common_op_fail: "Failed",
    common_op_refresh: "Refresh",
    common_op_new: "Add",
    common_op_del: "Delete",
    common_op_publish: "Publish",
    common_op_modify: "Modify",
    common_op_edit: "Edit",
    common_op_preview: "Preview",
    common_op_detail: "Details",
    common_op_view: "View",
    common_op_move: "Move",
    common_op_search: "Search",
    common_op_reset: "Reset",
    common_op_return: "Return",
    common_op_exit: "Exit",
    common_op_hiddenSearch: "Hide Search",
    common_op_showSearch: "Search",
    common_createdDate: "Create Date",
    common_createdTime: "Create Time",
    common_uploadTime: "Update Time",
    common_startDate: "Starting Date",
    common_endDate: "Ending Date",
    common_startTime: "Start Time",
    common_endTime: "End Time",
    common_date_interval: "Date Interval",
    common_time_interval: "Time Interval",
    common_select_dateInterval: "Select the Range of Dates",
    common_select_timeScope: "Select Time Scope",
    common_select_startTime: "Starting Time",
    common_select_endTime: "Ending Time",
    common_now: "Now",
    common_monday: "Monday",
    common_tuesday: "Tuesday",
    common_wednesday: "Wednesday",
    common_thursday: "Thursday",
    common_friday: "Friday",
    common_saturday: "Saturday",
    common_sunday: "Sunday",
    common_close: "Close",
    common_open: "Open",
    common_download: "Download",
    common_finish: "Finish",
    common_op_export: "Export",
    common_op_import: "Import",
    common_audit: "Audit",
    common_remark: "Remark",
    common_all: "All",
    common_from: "From",
    common_to: "To",
    common_select: "Select",
    common_gender: "Gender",
    common_male: "Male",
    common_female: "Female",
    common_relatedOp: "Operation",
    common_opUser: "Operational User",
    common_module: "Public",
    common_name: "Name",
    common_number: "No.",
    common_title: "Title",
    common_default: "Default",
    common_carousel: "Carousel",
    common_caption: "Caption",
    common_car: "Vehicle",
    common_ad: "Advertisement",
    common_leisure: "Idle",
    common_busy: "Busy",
    common_duration: "Duration",
    common_enable: "Enable",
    common_stop: "Stop",
    common_disable: "Disable",
    common_normal: "Normal",
    common_exception: "Exception",
    common_status: "Status",
    common_online: "Online",
    common_offline: "Offline",
    common_unknown: "Unknown",
    common_group: "Group Name",
    common_group_all: "All Groups",
    common_picture: "Picture",
    common_audio: "Audio",
    common_video: "Video",
    common_file: "File",
    common_other: "Others",
    common_size: "Size",
    common_format: "Format",
    common_property: "Property",
    common_value: "Value",
    common_resolution_ratio: "Resolution",
    common_select_place_place: "Please select a place to place",
    common_advertiser: "Advertiser",
    common_select_advertiser: "Select a Advertiser",
    common_minute: "Minute",
    common_second: "Second",
    common_page: "Page",
    common_page_previous: "Previous Page",
    common_page_next: "Next Page",
    common_nextStep: "Next Step",
    common_preStep: "Previous Step",
    common_sort: "Sort",
    common_role: "Role",
    common_remarks: "Remarks",
    common_user_name: "User Name",
    common_user_password: "Password",

    common_user_sex: "Gender",
    common_user_email: "Email",

    common_user_role: "User Role",
    common_mobile: "Mobile Number",
    common_user_status: "User Status",
    common_user_number: "User Code",
    common_user_nickname: "Nickname",
    common_dept_name: "Department",
    common_plan_name: "Schedule name",
    common_ascription_dept: "Department",
    common_input_volume: "Please input the Volume",
    common_input_osVersion: "Please input the System Version",
    common_input_scroll_text: "Please enter the text you want to scroll",
    common_input_macAddress: "Please input the MAC Address",
    common_input_user_password: "Please input the User Password",
    common_width: "Width",
    common_height: "Height",
    common_input_width: "Please input the Width",
    common_input_height: "Please input the Height",
    common_input_resolution_ratio: "Please input the Resolution",
    common_input_group_name: "Please input the Group Name",
    common_input_group_type: "Please input the Group Type",
    common_input_material_name: "Please input the Material",
    common_input_programs_name: "Please input the Program",
    common_input_template_name: "Please input the Template Name",
    common_input_screen_name: "Please input the Screen Name",
    common_input_scheduling_name: "Please input a schedule name",
    common_input_scheduling_type: "Please input the play Plan Type",
    common_input_deviceId: "Please input Device ID",
    common_input_device_name: "Please input the Device Name",
    common_input_device_code: "Please input the Device Code",
    common_input_device_address: "Please input the Device Address",
    common_input_commercial_owner: "Please input the  Principal",
    common_input_version_name: "Please input the Version Name",
    common_input_version_code: "Please input the Version Code",
    common_input_version_appDesc: "Please input the Version Description",
    common_input_log_title: "Please input the Log Title",
    common_input_dept_name: "Please input the Departmental Name",
    common_input_role_name: "Please input the Role Name",
    common_input_user_name: "Please input the User Name",
    common_input_menu_name: "Please input the Menu name",
    common_input_dict_name: "Please input the Dictionary",
    common_input_config_name: "Please input the Configuration Name",
    common_input_config_key: "Please input the Configuration Key",
    common_input_backup_name: "Please input the Backup Name",
    common_input_job_name: "Please input the Task Name",
    common_input_job_groupName: "Please input the task group name",
    common_input_login_user: "Please input the login user",
    common_input_login_address: "Please input the Login Address",
    common_input_name: "Please input the Name",
    common_input_title: "Please input the Title",
    common_input_duration: "Please input the Duration",
    common_input_operName: "Please input the Operator's Name",
    common_input_phone: "Please input the Mobile Number",
    common_input_city: "Please input the City",
    common_input_company_name: "Please input the Company Name",
    common_input_persons: "Please input the number of Persons",
    common_input_contacts: "Please input the Contact Person",
    common_input_email: "Please input the Email",
    common_input_address: "Please input the Address",
    common_input_products_suppliers: "Please input the Supplier's name",
    common_input_user_nickname: "Please input the Nickname",
    common_input_reason: "Please input the reason of the denial",
    common_input_caption_title: "Please input the caption title",
    common_input_caption_connect: "Please input the caption content",
    common_input_playback_duration: "Please input the duration",
    common_input_remarks: "Please input the remark",
    common_input_old_password: "Please input the Old Password",
    common_input_new_password: "Please input the New Password",
    common_input_confirm_password: "Please confirm the password",
    common_select_status: "Please select the status",
    common_select_type: "Please select the type",
    common_select_group: "Please select the group",
    common_select_leftGroup: "Please select a Group on the left",
    common_select_groupOrDevice: "Please select by Group or by device",
    common_select_material: "Please select the material",
    common_select_program_type: "Please select the program type",
    common_select_publish_type: "Please select the publish type",
    common_select_download_status: "Please select the download status",
    common_select_download_time: "Please select the download time",
    common_select_playWeeks: "Please select the playback week",
    common_select_playMode: "Please select the playback mode",
    common_select_captionPosition: "Please select the caption's location",
    common_select_direction: "Please select the direction",
    common_select_speed: "Please select the speed",
    common_select_fontSize: "Please select the font size",
    common_select_fontColor: "Please select the font color",
    common_select_bgColor: "Please select the background color",
    common_select_dept: "Please select the department",
    common_select_device: "Please select the device",
    common_select_device_type: "Please select the device type",
    common_select_rotationDegree: "Please select the rotation degree",
    common_select_device_playMode: "Please select the playback mode",
    common_select_version: "Please select the version",
    common_select_version_type: "Please select the version type",
    common_select_operation_status: "Please select the operation status",
    common_select_dept_status: "Please select the Department status",
    common_select_role_status: "Please select the Role status",
    common_select_user_status: "Please select the User status",
    common_select_menu_status: "Please select the Menu status",
    common_select_dict_status: "Please select the Dictionary status",
    common_select_config_status: "Please select the Config status",
    common_select_config_sys_status:
      "Please define if this is a built-in system",
    common_select_region: "Please select the Region",
    common_select_job_type: "Please select the Task type",
    common_select_job_status: "Please select the Task status",
    common_select_job_executeStatus: "Please select the execute status",
    common_select_login_status: "Please select the login status",
    common_select_continents: "Please select the Continent",
    common_select_country: "Please select the Country",
    common_select_industry: "Please select the Industry",
    common_select_ascription_dept: "Please select the Department",
    common_rule_program_name: "The Program Name cannot be empty",
    common_rule_program_type: "The Program Type cannot be empty",
    common_rule_template_name: "The Template Name cannot be empty",
    common_rule_resolution: "The Resolution cannot be empty",
    common_rule_group_name: "The Group Name cannot be empty",
    common_rule_name_len60: "The Name length cannot exceed 60 digits",
    common_rule_area_len60:
      "The length of the region name cannot exceed 60 digits",
    common_rule_email_len60: "The email length cannot exceed 60 characters",
    common_rule_phone_len60: "The phone length cannot exceed 60 digits",
    common_rule_caption_title: "The Caption title cannot be empty",
    common_rule_caption_connect: "The Caption content cannot be empty",
    common_rule_device_name: "The Device name cannot be empty",
    common_rule_device_code: "The Device code cannot be empty",
    common_rule_device_type: "The Device type cannot be empty",
    common_rule_device_mac: "The MAC address cannot be empty",
    common_rule_device_name_len120:
      "The length of the Device name cannot exceed 30 digits",
    common_rule_device_mac_len255:
      "The length of the MAC address cannot exceed 30 bits",
    common_rule_version_name: "The Version name cannot be empty",
    common_rule_version_code: "The Version code cannot be empty",
    common_rule_version_type: "The Version type cannot be empty",
    common_rule_version_appDesc: "The Version  description cannot be empty",
    common_rule_version_name_len30:
      "The Version name length cannot exceed 30 bits",
    common_rule_version_code_len30:
      "The Version code length cannot exceed 30 bits",
    common_programNotSelect: "Please select at least one program",
    common_deviceNotSelect: "Please select at least one device",
    common_audit_info:
      "Auditors have the responsibility to audit unauthorized, negative, bad information, or pornographic, violent information. We shall be liable for any legal liability and other consequences arising therefrom",
    common_upload_drag: "Drag to Upload File",
    common_upload_click: "Click to Upload",
    common_add_click: "Click to Add",
    common_upload_apk: "Only apk files are allowed to be uploaded",
    common_fontSize: "Font Size",
    common_fontColor: "Font Color",
    common_bgColor: "Background Color",
    common_yes: "Yes",
    common_no: "No",
    common_pre: "Last Step",
    common_next: "Next Step",
    common_submit: "Submit",
    common_no_result_dept: "The agency does not exist",
    common_no_result_group: "The group does not exist",
    common_please_select: "Please choose",
    common_msg_add_success: "Successfully Added!",
    common_msg_update_success: "Successfully Modified!",
    common_msg_del_success: "Successfully Deleted!",
    common_old_password: "Old Password",
    common_new_password: "New Password",
    common_confirm: "Confirm",
    common_confirm_password: "Confirm Password",
    common_update_password: "Update Password",
    common_icon: "Icon",
    common_click_icon: "Select Icon",
    common_path: "Path",
    common_input_path: "Enter the Path",
    common_background_color: "Bg-Color",
    common_input_base_name: "The Name cannot be empty",
    common_input_path_null: "The Path cannot be empty",
    common_path_length: "The Path length cannot exceed 120 bits",
    common_input_sort_null: "Sort cannot be empty",
    common_role_name: "Role Name",
    common_role_status: "Role Status",
    common_role_no: "Role Number",
    common_role_order: "Role Order",
    common_display_order: "Display Order",
    common_data_rights: "Data Authority",
    common_menu_rights: "Menu Authority",
    common_loading: "Loading",
    common_rights_code: "Code Authority",
    common_rights_range: "Scope of Authority",
    common_role_name_null: "Role Name cannot be empty",
    common_success: "Successful",
    common_sort_number: "Sort Number",
    common_failed: "Failed",
    common_upload: "Upload",
    common_share: "Share",
    common_list: "List",
    common_device_total: "Total Devices",
    common_device_online: "Online Devices",
    common_program_total: "Program List",
    common_to_do_list: "To-Do List",
    common_input_code: "Please enter 0 or 1",
    common_input_errorMessage: "Incorrect Volume Format",
    common_device_errorMessage: "No device information. Please try again.",
    common_brightness_errorMessage: "Incorrect Brightness Format",
    common_refresh_page: "Refresh",
    common_close_current: "Close the current page",
    common_close_other: "Close the other page",
    common_close_all: "Close All",
    common_quit_system: "Exit",
    common_confirm_quit_system:
      "Are you sure you to log out and exit the system?",
    common_error_000:
      "The operation is too frequent. Please do not repeat the request",
    common_error_401: "The authorization has expired. Please login again",
    common_error_403: "The current operation has no permission",
    common_error_404: "The resource does not exist",
    common_error_417:
      "The login account is not bound. Please use the password to log in and bind it",
    common_error_423:
      "Demo environment cannot be operated. Please contact ZKTeco staff for details",
    common_error_426:
      "The user name does not exist or the password is incorrect",
    common_error_428: "Verification code error. Please enter again",
    common_error_429: "Request is too frequent",
    common_error_430: "Verification code has expired",
    common_error_479: "Demo environment, no operation permission",
    common_error_default:
      "Unknown system error. Please send feedback to the admin",
    common_login_timeout: "Login Timeout",
    common_product_error:
      "The product authorization is invalid. Please contact the manufacturer",
    common_system_prompt: "System Prompt",
    common_login_again: "Log in again",
    common_company_text: "ZKTECO CO., LTD",
    common_status_text: "Slow Down",
    common_number_text: "Automatic Identification",
    common_time_text: "Current Time",
    common_acc_note_text:
      "Note: When the half-screen mode is selected on the device side, the content below the dotted line will be covered.",
    common_password_inconsistency: "Inconsistent entry password",
    common_system_version: "Version",
    common_material_conversion_unable_view:
      "Material conversion failed, unable to preview",
    common_material_conversion_progress_unable_view:
      "Material conversion in progress, please wait",
    common_connot_lower_zero: "Cannot be lower than 0",
    common_company_type: "Please select a company type",
    // add
    common_user: "User",
    common_company: "Company",
    common_company_information: "Company information",
    common_company_switch: "Switching company",
    common_comapny_create: "Create company",
    // ADD1
    common_first_name: "First Name",
    common_last_name: "Last Name",
    common_email: "Email",
    common_input_first_name: "Please enter your first name",
    common_input_last_name: "Please enter your last name",
    common_input_mobile: "Please enter your telephone number",
    // add2
    common_agree_desc: "Please check the box and read the agreement",
    common_form_agree: "I have read and agree",
    common_user_agree: "《User Agreement》",
    common_and: "And",
    common_name_as: "Be named as",
    common_times_as: "The duration is",
    common_private_agree: "《Privacy Policy》",
    common_forgot: "Forgot?",
    common_form_username_input: "email address",
    common_form_password_input: "password",
    common_form_verification_input: "Verification code",
    common_form_username_placeholder: "Please enter code",
    common_form_password_placeholder: "Please enter password",
    common_form_code_placeholder: "Please enter the verification code",
    common_form_username_validation:
      "The mobile phone number or email address is mandatory",
    common_form_account_validation: "Account is required",
    common_form_password_validation: "The password is mandatory",
    common_form_area_validation: "The area code is mandatory",
    common_form_code_validation: "The verification code is mandatory",
    common_form_get_code: "Get Verification Code",
    common_form_get_code_surplus: " seconds left",
    common_form_user_code_validation:
      "The mobile phone number or email verification code is mandatory",

    // ADD3
    common_form_rule_msg: "The cell phone number or mailbox can not be empty",
    common_firstname_placeholder: "First Name",
    common_lastname_placeholder: "Last Name",
    common_op_batch_del: "Batch deletion",
    // add4
    common_check_data: "Please check the data you want to manipulate first",
    common_plese_select_file: "Please select the file",
    common_uploaded_wait: "Please wait while the file is uploaded",
    common_please_select_com: "Please Select",
    common_component_uoload_empty: "The upload file can not be empty",
    common_component_upload_max_length:
      "You cannot attach more than {{maxUploadFiles}} files",
    common_component_uoload_file_max_size:
      "The size of files cannot exceed {{maxFileSize}}Mb",
    common_component_uoload_file_extension:
      "Extension .{{extension}} has been excluded",
    // add5
    common_upload_remove_all: "Remove all",
    // ADD6
    common_add_program: "New programs",
    common_confirm_delete_program:
      "Are you sure you want to delete the program name: {{ids}} ?",
    // 保存修改
    common_save_change: "Save changes",
    common_forgort_title: "Forget your password",
    common_person_number_max: "More than 10000",
    common_company_name: "Company name",
    common_company_create: "Create a company organization",
    common_company_size: "Company size",
    common_label_company_type: "Type of company",
    common_login_select_company_size: "Please select company size",
    common_export_error: "Error exporting download file!",
    common_conn_server_error:
      "The back-end interface is improperly connected. Procedure",
    common_server_timeout: "The system interface request timed out",
    common_server_unknown_error: "System interface {{code}} is abnormal",
    common_confirm_delete_channel:
      "Do you want to delete a signage whose channel name is: {{ids}}?",
    common_confirm_delete_device:
      "Do you want to delete the signage whose name is {{ids}}?",
    common_confirm_delete_link_screen:
      "Confirm whether to delete the screen name as: {{ids}} ?",
    common_confirm_delete_merchant:
      "Do you want to delete the  Principal whose name is {{ids}} ?",
    common_confirm_link_delete_device:
      "Deleting {{ids}} digital tag will synchronously delete all playback plans in the tag. Are you sure to delete it?",
    common_confirm_link_delete_link_screen:
      "Deleting {{ids}} screen will synchronously delete all playback plans in that screen. Are you sure to delete?",
    common_confirm_reboot_device:
      "Confirm whether to restart the signage name is {{ids}} ?",
    common_confirm_sync_time_device:
      "Confirm whether to synchronize the signage at：{{ids}} ?",
    common_select_one_operation:
      "Only one object can be selected for operation",
    common_add_devops: "New Operator",
    common_add_retailer: "New Employee",
    common_add_advertiser: "New Employee",
    //todo
    common_add_retailerTitle: "New Employee of Principal",
    common_add_advertiserTitle: "New Employee of Advertiser",
    common_confirm_delete_location_type:
      "Do you want to delete the location type whose name is {{ids}} ?",
    common_confirm_delete_application:
      "Confirm whether to delete the application name as:{{ids}} ?",
    // 区域
    common_area_name: "Area Name",
    common_input_area_name: "Please enter a region name",
    common_area_all: "All Areas",
    common_belong_area: "Superior Area",
    common_area_accelerate: "Area Accelerate",
    common_select_area: "Please select an area",
    // 新增 2023/7/25
    common_table_query: "Query",
    common_form_unfold: "More",
    common_form_packup: "Retract",
    common_please_input_email: "Please enter your contact email",
    common_please_input_screen_number: "Please enter the number of screens",
    common_please_input_brand_name: "Please enter the Principal name",
    common_brand: "Principal",
    common_please_input_store_name: "Please enter the Outlet ",
    common_oper_edit_store: "Modify Outlet",
    common_mobile_area: "Area code",
    common_select_retail: " Select a Principal",
    common_please_select_retail: "Please select a Principal",
    common_form_area_name: "Area",
    common_store_location: "Store Location",
    common_store_total: "Store Total",
    common_store_name_notnull: "The Outlet cannot be empty",
    common_store_length_max:
      "The Outlet cannot exceed 100 characters in length",
    common_contact_name_notnull: "The contact cannot be empty",
    common_contact_length_max: "The contact name cannot exceed 30 characters",
    common_contact_phone_notnull: "The contact number cannot be empty",
    common_store_location_notnull: "The location of the Outlet cannot be empty",
    common_edit_sotre: "Modify Outlet Information",
    common_current_location_error: "Failed to get the current location",
    common_select_location: "Select Location",
    common_input_location_search: "Enter location information to search",
    common_search_info: "Search Information",
    common_location_e: "The location is",
    common_confirm_location: "Confirm the selection of the location",
    common_select_current_location:
      "The current selected city is {{address}},and the selected longitude and latitude are {{lng}}, {{lat}}.",
    common_current_location_e: "The current Outlet location is",
    common_current_lng_lat: "Latitude and longitude information",
    common_store_location_info: "Outlet Information",
    common_please_input: "Please enter",
    common_outlet_owner: "Outlet",
    common_location: "Location",
    common_add_screen: "Add Digital Signage",
    common_edit_screen: "Edit Signage",
    common_number_screen_list: "Digital signage list",
    common_screen_reboot: "Reboot",
    common_plese_select: "Please Select",
    common_show_screen_sn: "Please check the SN on the back of the machine",
    common_loading_select_outlet: "Select the outlet and then backfill",
    common_add_screen_alert_title:
      "Digital signage is energized and connected to the Internet",
    common_add_screen_alert_desc: `
    1.Enter the digital signage Ethernet Settings or WiFi Settings menu, enter the communication Settings page Network Settings successfully.
    <br />
    2.The server connection address is set, and the server is set successfully. <br />
    3.On the side of the digital signage box or the back of the digital signage, you can find the digital signage Sn.
    <br />
    4.Fill in the digital label Sn on the system.
    `,
    common_rule_device_name_length_max:
      "Enter a digital signage name less than 20 in length",
    common_plese_screen_sn: "Please enter the digital label SN",
    common_screen_sn_notnull: "Digital label SN cannot be empty",
    common_screen_sn_length_max: "Digital label SN cannot be empty",
    common_plese_screen_direction: "Please select digital signage directions",
    common_plese_scrren_onwer_outlet: "Please select your Outlet",
    common_screen_detail: "Digital signage Details",
    common_link_screen_name: "Associated Signage Name",
    common_link_screen_list: "Associated signage list",
    common_link_screen_num: "Digital Signage Qty.",
    common_link_screen_specifca: "Specification",
    common_Unable_load: "Unable to load",
    common_link_column: "Column",
    common_link_row: "Line",
    common_link_add: "Add Screen",
    common_confirm_delete_store: `Are you sure you want to delete the data entry for outlet with name "{{names}}"?`,
    common_error_email: "Please enter a valid email address.",
    common_error_phone: "Please enter a valid phone number.",
    common_error_phone_area: "Please enter a valid mobile phone area code.",
    common_upload_type_not_support: "The current type is not supported",
    common_upload_file_tips: `Maximum number of uploads :6 </br> Maximum file size :1GB</br>Supported formats for resources include :`,

    // 8/25
    common_upload_all_message_success:
      "Upload of all files completed successfully",
    common_drage_branch_file: "Drag or browse",
    common_add_create_advertiser: "Add Advertiser",
    common_add_create_retailer: "Add Principal",
    common_select_brand: "Please select an brand",
    common_advertiser_name: "Advertiser",
    common_retailer_name: "Principal Name",
    common_please_retailer_name: "Please Principal name",
    common_please_advertiser_name: "Please Advertiser name",
    common_upload_name_exist:
      "File name: {{name}}, Already exists, Cannot upload",
    common_upload_process: "Upload Process",
    common_Background_upload: "Background Upload",
    common_login_account: "Login Account",
    common_login_user_name: "Login User Name",
    common_login_ip: "Login IP",
    common_login_address: "Login Address",
    common_access_time: "Access Time",
    common_login_message: "Login Message",
    common_login_status: "Status",
    common_operation: "Operation",
    common_login_success: "Successful login",
    common_login_fail: "Failed login",

    // 8/28
    common_upload_image_tips: `Limit number of files to upload: 1<br>Files cannot exceed 100MB in size<br>Compatible file formats`,

    common_cover_upload: "Cover upload",
    common_select_image: "Select image",
    common_upload_file_size_max:
      "File size exceeds limit: {{fileSize}}MB. Please upload files that are smaller than or equal to 100MB.",
    common_upload_file_size_max_1G:
      "File size exceeds limit: {{fileSize}}MB. Please upload files that are smaller than or equal to 1 GB.",
    common_operation_method: "Operation Method",
    common_operation_account: "Operation Account",
    common_operation_IP: "Operation IP",
    common_operation_address: "Operation Address",
    common_operation_time: "Operation Time",
    //8/29
    common_system_resoltion_recommond:
      "Suggested screen resolution: 1920x1080 or higher",
    common_store_space: "System Storage Space",
    common_system_flow: "System Flow",
    common_security_settings: "Security Settings",
    common_use_total: "used {{use}}GB / total {{total}}GB",
    common_change_avatar: "Change avatar",
    common_tailor: "Tailor",
    common_zoom: "Enlarge",
    common_reduce: "Narrow",

    common_account_tips: "Please input Mobile phone number or email address",
    common_please_change_password:
      "For account security, please change the initial password",
    common_time_not_repeat_annotation:
      "Note: Cannot set a repeating time period.",
    common_duraation_annotation:
      "Note: If the set playback duration is greater than the video duration, the carousel will be played, and if the video duration is less than the video duration, the playback will be interrupted.",
    common_set_duration: "Set playback duration",
    common_area_name_not_null: "The area cannot be empty",
    common_input_sort: "Please input the Volume",

    //9/7
    common_clear_operation: "Clear Operation",
    common_warning_prompt: "Warning prompt:",
    common_data_loss_risk: "Data loss risk:",
    common_op_warn_text_1:
      "Risk of data loss: Deleting operation logs permanently deletes all recorded operation history, including modification and other critical operations. Once emptied, the log data cannot be recovered.",
    common_incomplete_audit_trail: "Incomplete audit trail:",
    common_op_warn_text_2:
      "Operation logs are an important audit tool for tracking and tracing events that occur in the system. Clearing operation logs may result in an incomplete audit trail, making subsequent security investigations and troubleshooting more difficult.",
    common_login_warn_text_1:
      "Clearing login logs permanently deletes all recorded login histories, including key information such as login time and IP address. Once emptied, the log data cannot be recovered.",
    common_security_investigation_difficult:
      "Security investigation is difficult:",
    common_login_warn_text_2:
      "Login logs are an important security tool used to monitor and detect potential security threats and unauthorized access. Clearing login logs may make subsequent security checks and investigations difficult, making it impossible to trace and analyze potential security incidents.",

    // 9/8
    common_content_name: "Content Name",
    common_select_schedule: "Select Schedule Type",
    common_start_date: "Start Date",
    common_end_date: "End Date",
    common_content_source: "Source of content",
    common_account_not_null: "Account cannot be empty",
    common_enter_account_phone: "Please enter your account(email)",
    common_only_file_apk: "Only allow uploading files in apk format",
    common_select_package_type: "Select Upgrade Package Type",
    common_upload_upgrade_package: "Upload upgrade package and upgrade",
    common_upgrade: "Upgrade",
    common_batch_upgrade: "Batch upgrade",
    common_enter_package_version:
      "Please enter the upgrade package version, starting with the letter 'V'",
    common_enter_limit_start_v: "Must start with the letter V",

    // 11/6
    common_area_center_location: "Region center point location",
    common_please_area_center_location:
      " Please select the region center point location.",
    common_delete_confirm_description: "Please confirm whether to delete!",
    common_name_year: "Year",
    common_name_month: "Month",
    common_please_enter_year: "Please enter the year",
    common_please_enter_month: "Please enter the month",
    common_los_merchant_name: "Affiliated Principal",
    common_los_area_name: "Owning region",
    common_year_and_month: "Year and month",
    common_total: "Totality",
    common_btn_add_outlet: "Add Outlet Groups",
    common_title_add: "Add Outlet Groups",
    common_title_edit: "Edit Total Outlet",
    common_please_year_and_month: "Please select the year and month",
    common_total_min_0: "The total cannot be less than 0",
    common_total_please: "Please enter total",
    common_merchant_please_logo: "Please upload the brand identity",
    common_merchant_logo: "Brand identity",
    common_file_max_size: "File size must not exceed, {{size}}",
    common_please_device_Alias: "Please enter a digital signage alias",
    common_export_type: "Export type",
    common_export_type_one: "Export current page data",
    common_export_type_zero: "Export all data",
    common_export_field: "Export fields",
    common_export_tips:
      "Tips: If not selected, all fields will be exported by default",
    common_export_btn: "Export",
    common_export_screen_title: "Export Digital Signage data",
    common_export_store_title: "Export Outlet data",
    common_export_sub_record_title: "Export subscription record data",
    // xinzeng
    common_download_error: "Download failure",
    common_import_error: "Import failure",
    common_import_template_name: "Outlet Import Template",
    common_import_store_title: "Import Outlet Data",
    common_import_type: "Import Type",
    common_import_type_one: "Overwrite Existing Data",
    common_import_type_two: "Do Not Overwrite Existing Data",
    common_import_extends1_title: "Overwrite Existing Data:",
    common_import_extends1_desc:
      "If the Outlet name already exists, it will be updated; if not, it will be saved.",
    common_import_extends2_title: "Do Not Overwrite Existing Data:",
    common_import_extends2_desc:
      "If there are duplicate Outlet names, the import will fail.",
    common_import_upload: "Upload File",
    common_download_template_tips:
      "Please update the template before each import.",
    common_download_template_btn: "Click to Download Template",
    common_import_tips1: "Please follow the table rules for importing.",
    common_import_tips2: "The format for import should be .xlsx.",
    common_import_tips3: "Try not to exceed 500 records for a single import.",
    common_import_next: "Next",
    common_import_receipt: "Import Receipt",
    common_import_success_num: "Number of Successful Imports: {{num}} records",
    common_import_error_num: "Number of Failed Imports: {{num}} records",
    common_import_loading: "Importing Data",
    common_import_loading_desc: "Importing data... Please wait patiently.",
    common_import_loading_desc2:
      "For large datasets, the import process may take longer.",
    common_layout: "layout",

    common_play_list_type_layout: "Layout List",
    common_add_layout_resource: "Layout Resources",
    common_not_add_layout_resource:
      "Layout resources and video images cannot coexist at the same time",

    //1/19
    common_copy_playlist_tips:
      "Confirm to overwrite and copy the imported playlist!",
    common_advanced_menu_title: "Advanced Features",
    common_input_interval_time: "Please enter a custom interval time",
    common_copy_row: "Copy selected rows",
    common_copy_row_null_playlist:
      "The selected copy contains empty playlists or playback times",
    common_please_select_row_copy: "Please select the rows to be copied",
    common_please_add_new_row: "Add a new row",
    common_copy_input_info: "Copy input information",
    common_copy_tips_info:
      "Copy based on the last one, and increment the playback schedule according to the selected time interval.",
    common_interval_time: "Time interval",
    common_interval_time_15_minute: "15 min",
    common_interval_time_30_minute: "30 min",
    common_interval_time_60_minute: "60 min",
    common_interval_time_customer_minute: "Custom unit/min",
    common_duration_minute: " Duration/min",
    common_copy_ok: "Confirm Copy",
    common_screen_shot_detail: "Screenshot details",
    common_screen_base_info: "Basic information",
    common_screen_shot_time: "Screenshot time",
    common_screen_copy_del_error: "Select the Row that you want to delete",
    common_screen_shot_preview_thumbnail: "Thumbnail",
    common_copy_erro_null: "Failed to import if the list is empty",
    common_screen_colour_temperature: "Device Color Temperature",
    common_screen_base_tab: "Basic Configuration",
    common_screen_controller_tab: "Device Control",
    common_free: "Free",
    common_package_price: "Product pricing information",
    common_package_details: "Package Details",
    common_remaining_day: "Remaining Valid Days",
    common_no_limit: "No Limit",
    common_used_day: "Used: {{day}}  days",
    common_gross: "Total",
    common_day: "days",
    common_remaining_device_count: "Remaining Device Count (units)",
    common_add_device_num: "Added: {{count}} units",
    common_add_device_dot: "Units",
    common_deviceType: "Device type",
    common_please_type: "Please select the device type",

    common_table_count: "Total:{{total}} items",
    common_publish_scheduleName:
      "The plan name for one-click delivery is as follows:",
    common_schedule_copy: "Copy",
    common_copy_schedule_playList: "Copy Playlist Schedule",
    common_start_date_end_date: "Start Date - End Date",
    common_please_screen_store: "Select Device Store",
    common_select_rule_screen:
      "Please select screens with the same specifications as the copy list",
    common_schedule_publish_btn: "Publish",
    common_copy_text: "- Copy",
    common_material_category: "Material Group",
    common_material_category_please: "Please select a material group",
    common_empty_select: "No data available",
    common_material_category_button: "Material Group",
    common_material_category_name: "Group Name",
    common_material_category_name_please: "Please enter the group name",
    common_material_category_table_column_name: "Group Name",
    common_material_category_table_column_merchant: "Principal",
    common_material_category_table_column_sort: "Sort",
    common_material_category_add_button: "Add Group",
    common_material_category_add_title: "Add Group",
    common_material_category_edit_title: "Edit Group",
    common_material_remove_one_tips:
      "Are you sure you want to delete the group '{{name}}'? This action cannot be undone",
    common_material_remove_more_tips:
      "Are you sure you want to delete these groups '{{names}}'? This action cannot be undone",
    common_material_category_name_length_rule:
      "Group name must be between 1 and 50 characters long",
    common_oneclick_description_tips:
      "The possible causes of the publishing failure are: 1. The device is offline; 2. The IOT connection is incorrect.",
    common_parent_material_group: "Parent Material Group",
    common_parent_material_group_please:
      "Please select the parent material group",
    common_parent_material_group_tips:
      "Note: Maximum two levels of groups can be added",
    common_country_code: "Country Code",
  },

  //字典
  dictData: {
    dict_convert: "Processing",
    dict_pend_audit: "Pending for Audit",
    dict_audit_pass: "Approved",
    dict_audit_not_pass: "Not Approved",
    dict_convert_success: "Upload Successful",
    dict_convert_failed: "Upload Failed",
    dict_not_complete: "Incomplete Resources",
    dict_ordinary_program: "Ordinary Program",
    dict_car_program: "Car Program",
    dict_acc_program: "Access Program",
    dict_normal: "Normal",
    dict_stop: "Stop",
    dict_auth_status_modeSwitch: "Mode Switch Request",
    dict_auth_status_unverified: "Not Yet Verified",
    dict_issue: "Issued",
    dict_unIssue: "Not Issued",
    dict_android: "Android",
    dict_other: "Other",
    dict_success: "Successful",
    dict_failed: "Failed",
    dict_login_type: "Login Type",
    dict_login_type_app: "App Abnormal",
    dict_login_type_login: "Login",
    dict_login_type_logout: "Logout",
    dict_device_type_ad: "Advertising Box",
    dict_device_type_outDoorAd: "Outdoor Advertising Machine",
    dict_company_industry_it: "IT service",
    dict_company_industry_make: "Manufacturing",
    dict_company_industry_life: "Life service",
    dict_company_industry_school: "School education",
    dict_company_industry_government: "government",
    dict_company_industry_finance: "finance",
    dict_company_industry_entertainment: "Place of entertainment",
    dict_company_industry_medical: "Medical treatment",
    dict_company_industry_other: "Other industries",
  },

  system: {
    base_system_browsers: "Browser is recommended for this system",
    base_system_resolution: "Display Resolution",
    base_system_verification_code: "Verification Code",
  },
  //菜单
  menu: {
    page_not_found: "404",
    media_personal_about: "About",
    app_center: "ApplicationCenter",
    area: "Area",
    branch: "Branch",
    button_area_update: "Area Update",
    editor_branch: "Editor Branch",
    view_branch: "View Branch",
    branch_user_list: "Branch Employee List",
    branch_user_add: "Add Branch Employee",
    branch_user_view: "View Branch Employee",
    media_dashboard: "DashBoard",
    data_permission: "Data Scope Name",
    add_data_permission: "Add Data Scope",
    device_manager: "Device Managerment",
    add_device_manager: "Add Device",
    view_device_manager: "View Device",
    outlet_information: "Outlet Informention",
    product_Information: "Product Information",
    message_log: "Message Log",
    login_log: "Login Log",
    operation_log: "Operation Log",
    view_company: "View Company",
    userProfile_menu: "UserProfileMenu",
    parter_list: "Partner",
    parter_add: "Add Parter",
    parter_view: "View Parter",
    parter_user_list: "Parter Employee List",
    parter_user_add: "Add Parter Employee",
    parter_user_view: "View Parter Employee",
    permission_setting: "Permission Setting",
    permission_roles: "Permission Roles",
    principal_list: "Principal",
    principal_add: "Add Principal",
    principal_view: "View Principal",
    principal_user_list: "Principal Employee List",
    principal_user_add: "Add Principal Employee",
    principal_user_view: "View Principal Employee",
    retail_list: "Retail Main Data",
    outlet_add: "Add Outlet",
    outlet_view: "View Outlet",
    product_add: "Add Product",
    product_view: "View Product",
    scription_list: "Subscription",
    scription_add: "Add Subscription",
    scription_record_list: "Subscription Record",
    data_dict: "Data Dict",
    media_login: "Login",
    common_forgot: "Forgot Password",
  },

  product: {
    image: "Product Image",
    name: "Product Name",
  },

  ips: {
    ips_template_1: "Company Profile",
    ips_template_2: "New product conference_ has real objects",
    ips_template_3: "New product release_nonless object",
    ips_template_4: "Technology electronics",
    ips_template_5: "Summit",
    ips_template_6: "Exchange Forum",
    ips_template_7: "Exchange",
    ips_template_8: "Medical exchange",
    ips_template_9: "Doctor introduce",
    ips_template_10: "Luxury home",
    ips_template_11: "clothing",
    ips_template_12: "Education and Training Message Notice",
    ips_template_13: "New Notice of Education and Training Recruitment",
    ips_template_14: "Education and training_ graduation theme",
    ips_template_15: "Special event",
    ips_template_16: "Food",
    ips_template_17: "Cake class",
    ips_template_18: "Drink",
    ips_template_19: "Menu style 01",
    ips_template_20: "Menu style 02",
    ips_material: "Material Name",
    ips_material_code: "Material Number",
    ips_material_time: "Duration/Pages",
    ips_material_upload: "Upload Material",
    ips_advertising_resources_upload: "Upload ADs",
    ips_material_group: "Material Grouping",
    ips_material_select: "Select resources",
    ips_material_add: "Add Material",
    ips_material_edit: "Modify Material",
    ips_material_del:
      'Are you sure to delete the data with material number "{{id}}"?',
    ips_material_audit: "Are you sure you want to audit this material?",
    ips_select_auditor: "Please select the auditor",
    ips_auditor_person: "auditor",
    ips_program: "Program",
    ips_program_code: "Program Code",
    ips_program_scene: "Program Scene",
    ips_program_scene_num: "Number of Scenes",
    ips_program_type: "Program Type",
    ips_programDuration: "Program Duration",
    ips_program_durations: "Duration(s)",
    ips_program_duration: "Duration(s)",
    ips_program_durationss: "Durarion/s",
    ips_program_add: "Add a Program",
    ips_program_edit: "Modify a Program",
    ips_program_export_list: "Export the program list",
    ips_select_to_location:
      "Are you sure you want to export all the show data?",
    ips_program_export_data: "Export the program data",
    ips_program_export: "Are you sure to export all program data?",
    ips_programData_export: "Are you sure to export the selected program data?",
    ips_program_del: 'Are you sure to delete the program number"{{id}}"?',
    ips_template: "Template Name",
    ips_template_add: "Add a Template",
    ips_template_edit: "Modify a Template",
    ips_template_del:
      'Are you sure to delete the data with the template number"{{id}}"?',
    ips_template_export: "Are you sure to export all template data?",
    ips_program_scheduling: "Program Scheduling",
    ips_scheduling: "Schedule Name",
    ips_scheduling_code: "Schedule Code",
    ips_scheduling_audit: "Schedule for Editing",
    ips_scheduling_type: "Schedule Type",
    ips_scheduling_playMode: "Play Mode",
    ips_scheduling_local: "Local Play",
    ips_scheduling_online: "Play Online",
    ips_scheduling_playWeeks: "Repetition Mode",
    ips_scheduling_playDate: "Play Date",
    ips_scheduling_playTime: "Start Date-End Date",
    ips_scheduling_downloadTime: "Download Time",
    ips_scheduling_info: "Basic Information",
    ips_scheduling_program: "Select Program",
    ips_scheduling_device: "Select Device",
    ips_scheduling_add: "Add a Schedule",
    ips_scheduling_edit: "Modify a Schedule",
    ips_scheduling_del:
      'Deleting the playback plan will synchronously delete device data. Are you sure you want to delete the data with the playback plan name "{{id}}"?',
    ips_scheduling_publish:
      'Are you sure you want to publish a schedule named "{{id}}"?',
    ips_scheduling_export: "Are you sure to export all schedule data?",
    ips_caption_info: "Insert a message",
    ips_caption_info_audit: "Edit the Plug-in message",
    ips_caption_title: "Insert a Title",
    ips_caption_content: "Insert Content",
    ips_caption_position: "Insert a Location",
    ips_caption_mode: "Caption Mode",
    ips_caption_period: "On Time Period",
    ips_caption_timeLen: "On Instant Time",
    ips_caption_add: "Add a caption",
    ips_caption_edit: "Modify a caption",
    ips_caption_del:
      'Are you sure to delete the data with the insert message number"{{id}}"?',
    ips_caption_export: "Are you sure to export all insert data?",
    ips_device: "Digital Signage Name",
    ips_device_type: "Device Type",
    ips_deviceList: "Device List",
    ips_device_add: "Add a device",
    ips_device_edit: "Modify a device",
    ips_device_id: "Device Id",
    ips_device_code: "Device Code",
    ips_device_ip: "IP Address",
    ips_device_address: "Location",
    ips_device_status: "Device Status",
    ips_device_online: "Online Status",
    ips_device_softVersion: "Software Version",
    ips_device_auth: "Device Certification",
    ips_device_info: "Device Information",
    ips_device_overview: "Device Overview",
    ips_device_volume: "Device Volume",
    ips_device_curProgram: "Current Program",
    ips_device_runTime: "Device Operation Time",
    ips_device_memory: "Device Memory",
    ips_device_storage: "Device Storage Space",
    ips_device_scheduling: "Current Schedule",
    ips_device_checkTime: "Release Time",
    ips_device_screen: "Capture",
    ips_device_capture: "Screen Capture",
    ips_device_del:
      'Are you sure to delete the data with the device number"{{id}}"?',
    ips_device_export: "Are you sure to export all the device data?",
    ips_device_modeSet: "Are you sure to set the mode to the Cloud mode?",
    ips_device_clear: "Are you sure to clear the selected device data?",
    ips_device_mute: "Are you sure to mute the selected device?",
    ips_device_shutDown: "Are you sure to turn off the selected device?",
    ips_device_reboot: "Are you sure to restart the selected device?",
    ips_device_playStart:
      "Are you sure to start playing videos on the selected device?",
    ips_device_playStop:
      "Are you sure to stop playing videos on the selected device?",
    ips_device_screenShot:
      "Are you sure to capture the selected device screen?",
    ips_device_upgrade:
      "Are you sure to upgrade the selected device application version?",
    ips_device_backLight: "Backlight Settings (0 means Off, 1 means On)",
    ips_device_brightness: "Please input the brightness(0 to 255).",
    ips_device_screenSet:
      "Horizontal and Vertical Screen Settings (0 means Horizontal, 1 means Vertical)",
    ips_material_landscape: "Horizontal screen",
    ips_material_portrait_screen: "Vertical screen",
    ips_download_status: "Download Status",
    ips_download_times: "Number of Issuance",
    ips_download_progress: "Download Progress",
    ips_download_resend: "Re-issue",
    ips_playRecord_times: "Playback Times",
    ips_playRecord_detail: "Playback Details",
    ips_playRecord_del:
      'Are you sure to delete the playback record number"{{id}}"?',
    ips_resend_issue: 'Are you sure to resend the data numbered"{{id}}"?',
    ips_playRecord_export: "Are you sure to export all playback records?",
    ips_version: "Version Name",
    ips_version_code: "Version Number",
    ips_version_type: "Version Type",
    ips_version_appSize: "Version Size",
    ips_version_appDesc: "Description",
    ips_version_add: "Add a Version",
    ips_version_edit: "Modified Version",
    ips_version_del:
      'Are you sure to delete data with the version number"{{id}}"?',
    ips_version_export: "Are you sure to export all version data?",
    ips_appLog: "Log Title",
    ips_appLog_type: "Log Type",
    ips_appLog_info: "Log Information",
    ips_appLog_error: "Error Log",
    ips_appLog_message: "Log messages",
    ips_appLog_log: "Pulling logs",
    ips_appLog_pull_log:
      "Are you sure you want to pull the logs for this device?",
    ips_appLog_del:
      'Are you sure to delete data with the device application log number"{{id}}"?',
    ips_appLog_export: "Are you sure to export all device application logs?",

    ips_group_del: 'Are you sure to delete the Group"{{name}}"?',
    ips_group_exist: "The group name already exists!",
    ips_group_not_exist: "The group name does not exist!",

    ips_dept: "Organization",
    ips_store_name: "Outlet Name",
    ips_store_brand: "Principal",
    ips_store_location: "Position",
    ips_store_screen_number: "Digital Signage Qty.",
    ips_store_contacts: "Contact",
    ips_store_phone: "Mobile",
    ips_store_email: "Email",
    ips_store_address: "Address",
    ips_area_del: 'Are you sure you want to delete the "{{name}}" area?',
    ips_area_name: "Jurisdictional Area",
    ips_add_store: "New Outlet",
    ips_stay_area_name: "Area",
    // 2023/07/26
    ips_device_sn: "SN",
    ips_screen_direction: "Direction",
    ips_fwversion: "Firmware",
    ips_timezone: "TimeZone",
    ips_screen_alias: "Alias",
    ips_screen_brightnes: "Luminance",

    ips_store_client: "Principal",
    ips_store_outlet_name: "Outlet Name",
    ips_store_outlet_type: "Outlet Type",
    ips_store_outlet: "Outlet",
    ips_store_country: "Country",
    ips_store_province: "State/Province",
    ips_store_city: "City",
    ips_store_city_zone: "City Zone",
    ips_store_time_zone: "Time Zone",
    ips_store_client_name: "Principal",
    ips_store_region: "Region",
    ips_store_tips_select_country: "Please select a country",
    ips_store_tips_select_client: "Please select a Principal",
    ips_store_tips_select_province: "Please select a state/province",
    ips_store_tips_select_city: "Please select a city",
    ips_store_tips_select_outlet_type: "Please select a outlet type",
    ips_store_tips_select_city_zone: "Please select a city zone",
    ips_store_tips_select_outlet: "Please select a outlet",
    ips_status_monitoring: "Signage Status Monitoring",
    ips_signage_number: "Signage Quantity",
    ips_online_number: "Online Quantity",
    ips_offline_number: "Offline Quantity",
    ips_outlet_number: "Outlet Quantity",
    ips_online_count: "Online frequency",
    ips_offline_count: "Offline frequency",
    ips_offline_rate: "Offline Rate",
    ips_offline_online_count: "Offline frequency/Online frequency",
    ips_last_online_time: "Last Online Time",
    ips_resource_proportion: "Proportion",
    ips_total_resources: "Total Resources",
    ips_picture: "Picture",
    ips_media: "Video",
    ips_aduio: "Audio",
    ips_total: "Total",
    ips_traffic_statistics: "Traffic Statistics",
    ips_traffic_time: "Traffic/Time",
    ips_total_usage_traffic: "Personal used traffic",
    ips_merchant_name: "Principal Name",
    ips_merchant_type: "Principal Type",
    ips_type: "DMS Device Type",
    ips_resolution: "Resolution",
    ips_resolution_wide: "Resolution Width",
    ips_resolution_high: "Resolution Height",
    ips_resolution_input_number: "Please enter a number",
    ips_playlist_name: "Playlist Name",
    ips_selected_advertisements: "Selected Contents",
    ips_total_duration: "Total duration",
    ips_playlist_type: "Playlist Type",
    ips_new_playlist: "New Playlist",
    ips_editor_playlist: "Editor Playlist",
    ips_merchant: "Principal",
    ips_signage_name: "Signage Name",
    ips_playing: "Playing",
    ips_real_playback: "Real time playback data",
    ips_playlist_list: "Create Playlist List",
    ips_meida_image: "Video/Image",
    ips_digital_signage: "Digital signage schedule",
    ips_new_digital_signage: "New Digital Signage Schedule",
    ips_first_step: "1.Select a digital signage",
    ips_second_step: "2.Set basic information",
    ips_select_playlist: "Select Playlist",
    ips_please_select_playlist: "Please select a Playlist",
    ips_please_select_playlist_name: "Please select a Playlist Name",
    ips_select_month: "Please select a month",
    ips_select_date: "Please select a date",
    ips_playlist_exist: "List name or play time not selected",
    ips_schdule_publishing:
      "The schedule is currently being distributed, please do not click repeatedly",
    ips_at_least_one: "Please select at least one digital sign",
    ips_no_select_offline: "Offline digital signage cannot be selected",
    ips_starttime_greater_endtime:
      "End time is less than or equal to start time, please reselect",
    ips_overlap_play_time:
      "There is overlap in the playback time period, please reselect",
    ips_exceeds_one_year:
      "Please choose again if the time span exceeds one year",
    ips_all: "All",
    ips_month: "Month",
    ips_date: "Date",
    ips_screen_name: "Signage Name",
    ips_schedule_type: "Schedule Type",
    ips_startDate_endDate: "Start Date-End Date",
    ips_schedule_list: "PlayList",
    ips_please_select_shcedule_type: "Please select a schedule type",
    ips_signage_schedule: "Digital Signage Schedule",
    ips_link_schedule: "Link Schedule",
    ips_play_link_schedule: "Associated signage schedule",
    ips_new_link_schedule: "Add Associated Signage Schedule",
    ips_first_select_link: "1.Select a associated signage",
    ips_screen_group: "Screen group",
    ips_at_least_one_group: "Please select at least one screen group",
    ips_select_same_specifications:
      "Please select a linked signage of the same specification",
    ips_select_contain_offline:
      "The selected screen group contains offline signage",
    ips_reset: "reset",
    ips_add_playlist: "Add PlayList",
    ips_add_new_playlist: "New PlayList",
    ips_screen_specifications: "Screen specifications",
    ips_please_select_direction: "Please select a direction",
    ips_playlist_select: "Playlist Selection",
    ips_preview_time: "Playlist duration preview",
    ips_select_playlist_direction:
      "Please select the direction of the playlist",
    ips_select_playlist_type: "Please select a playlist type",
    ips_enter_playlist_name: "Please enter a playlist name",
    ips_add_media_iamge: "Add videos/images",
    ips_add_media_music: "Add background music",
    ips_add_resource: "New Resources",
    ips_append_playlist_list: "Append Playlist",
    ips_new_area: "New Area",
    ips_select_status: "Please select a status",
    ips_new_merchant: "New  Principal",
    ips_select_merchant: "Please select Principal",
    ips_select_merchant_type: "Please select  Principal type",
    ips_select_Principal_client: "Please select  Principal Client",
    ips_select_audit_status: "Please select the audit status",
    ips_edit_merchant: "Edit  Principal",
    ips_login_password: "Login Password",
    ips_sure_password: "Confirm Password",
    ips_belong_retail: "Principal",
    ips_belong_advertiser: "Advertiser",
    ips_enter_surname: "Please enter the user's last name",
    ips_enter_name: "Please enter the user's first name",
    ips_enter_email: "Please enter your email address",
    ips_enter_areacode: "Please enter the area code",
    ips_enter_phone: "Please enter your phone number",
    ips_enter_position: "Please enter the position",
    ips_enter_region: "Please select an area",
    ips_enter_password: "Please enter the password",
    ips_enter_password_again: "Please re-enter the login password",
    ips_select_retail: "Please select a Principal",
    ips_select_advertiser: "Please select a advertiser",
    ips_selected_outlet_list: "Selected Outlet list",
    ips_allocation_outlet: "Assign Outlet",
    ips_select_outlet: "Outlet Selection",
    ips_select_jurisdiction: "Select an area",
    ips_duration_is: "duration is",
    ips_name_is: "name is",
    ips_enter_playback_duration: "Please enter the playback duration",
    ips_duration_greater: "Duration greater than 1 second",
    ips_exist_media_audio: "Video and audio cannot coexist",
    ips_set_duration: "Set playback duration",
    ips_order: "Order",
    ips_edit_playlist: "Edit Playlist",
    // add
    ips_screen_list: "Screen PlayList",
    ips_link_screen_list: "LinkScreen PlayList",
    ips_playList_count_duration: "Playlist Duration",
    ips_enter_merchant_name: "Please enter the  Principal name",
    ips_enter_location_name: "Please enter the location type name",
    ips_add_location_type: "New Location Type",
    ips_select_location_type: "Please select the location type",
    ips_location_type: "Location Type",
    ips_location_type_name: "Location type name",
    ips_edit_location_type: "Edit Location Type",
    ips_digital_signage_schdule: "Digital signage schedule",
    ips_link_signage_schdule: "Linked screen schedule",
    ips_phone: "Mobile",
    ips_last_name_null: "Last name cannot be empty",
    ips_last_name_less30: "Last name length should be less than 30",
    ips_first_name_null: "First name cannot be empty",
    ips_first_name_less30: "First name length should be less than 30",
    ips_need_email_or_phone: "Either phone number or email is required",
    ips_email_error: "Invalid email address",
    ips_phone_error: "Invalid phone number",
    ips_phone_code_error: "Invalid phone area code",
    ips_length_less30: "Length should be between 6 and 30 characters",
    ips_password_different: "Passwords do not match",
    ips_enter_outlet_address: "Please enter the outlet address",
    ips_edit_pricipal: "Edit principal client user",
    ips_edit_devops: "Edit operator user",
    ips_edit_retail: "Edit Principal user",
    ips_not_select_single: "Not allowed to select 1*1",
    ips_not_reSelect_signage: "Cannot select the same digital signage again",
    ips_full_all_signage: "Please fill all the digital signage blocks",
    ips_enter_link_name: "Please enter the associated signage name",
    ips_link_name_null: "Link name cannot be empty",
    ips_link_name_lenth20: "Link name length should be less than 20",
    ips_select_a_outlet: "Please select an outlet",
    ips_select_rows: "Please select the number of rows",
    ips_select_colmuns: "Please select the number of columns",
    ips_select_loaction_to: "Select Loaction",
    ips_link_specifications: "Specifications (rows * columns)",
    ips_click_signage_bind:
      "Click on the digital signage block to select the binding digital signage",
    ips_signage_select: "Select Digital Signage",
    ips_not_select_outlet: "No outlet selected",
    ips_not_select_colmun_row: "No rows or columns selected",
    ips_offline_not_select: "Offline digital signage cannot be selected",
    ips_enter_location: "Please enter the location",
    ips_total_storage: "Total Storage",
    ips_sys_total_storage: "System Storage",
    ips_sys_used_storage: "System Used",
    ips_personal_data: "Personal Data",
    ips_self_use: "Personal Used",
    ips_system_last: "System Remain",
    ips_region: "Region",
    ips_total_number: "Total Number",
    ips_current_time_no_play: "No Schedule for Current Time Slot",
    ips_in_week: "Week",
    ips_in_month: "Month",
    ips_in_year: "Year",
    ips_sub_month: "Subscription Month",
    ips_sub_year: "Subscription Term",
    ips_resource_percent: "Person storage",
    ips_sys_used_storage_percent: "System Used",
    ips_total_outlet_num: "Outlet Number",
    ips_total_signage_num: "Signage Number",
    ips_outlet_address: "Outlet Address",
    ips_operator_name: "Operator Name",
    ips_operator_email: "Operator Email",
    ips_select_province: "Select Province",
    ips_select_region_county: "Select Region/County",
    ips_playlist_play_time: "Playlist Time",
    ips_schedule_detail: "Schedule Detail",
    ips_current_playlist: "Current Playlist",
    ips_link_schedule_detail: "Linked Schedule Detail",
    ips_digital_signage_name: "Digital Signage Name",
    ips_select_signage_status: "Select Signage Status",
    ips_enter_location_address: "Address",
    ips_system_used: "System Used",
    ips_screen_model: "Model",
    ips_screen_app_version: "App Version",
    ips_app_prod_version: "Official Version",
    ips_app_dev_version: "Beta Version",
    ips_publishing: "Publishing",
    ips_publish_fail: "Publish fail",
    ips_publish_finish: "Published",
    ips_no_publish: "Unpublished",
    ips_downloading: "Downloading",
    ips_wait_download: "To be downloaded",
    ips_download_fail: "Download failed",
    ips_download_success: "Download successful",
    ips_schedule_play_status: "Playback status",
    ips_schedule_LCD_L101_valid:
      "[Please note] The device's memory have selected is small, please check these limitations: 1. Only can synchronize one playlist to it. 2. The size of materials in the playlist cannot exceed 60MB.",
    ips_schedule_LCD_L101_Invalid:
      "Operation successful. Because of the device's memory have selected is small. After issuing a new playback plan, device existing playback plan will become invalid.",
    ips_oneClick_schedule_L101_valid:
      "If there is a device with limited memory in the pushed playback schedule, the existing playback plan will become invalid.",
    ips_sync_store_timezone: "Synchronize Time Zone",
    ips_confirm_sync_store_timezone:
      "Are you sure you want to synchronize the store time zone?",
    ips_select_site_timezone: "Please select the location and time zone",
    ips_timezone_device_setting: "Based on device settings",
  },

  table: {
    delete_warning: "Delete Warning",
    actions: "Actions",
    and: "and",
    cancel: "Cancel",
    changeFilterMode: "Change filter mode",
    changeSearchMode: "Change search mode",
    clearFilter: "Clear filter",
    clearSearch: "Clear search",
    clearSort: "Clear sort",
    clickToCopy: "Click to copy",
    collapse: "Collapse",
    collapseAll: "Collapse all",
    columnActions: "Column Actions",
    copiedToClipboard: "Copied to clipboard",
    dropToGroupBy: "Drop to group by {column}",
    edit: "Edit",
    expand: "Expand",
    expandAll: "Expand all",
    filterArrIncludes: "Includes",
    filterArrIncludesAll: "Includes all",
    filterArrIncludesSome: "Includes",
    filterBetween: "Between",
    filterBetweenInclusive: "Between Inclusive",
    filterByColumn: "Filter by {column}",
    filterContains: "Contains",
    filterEmpty: "Empty",
    filterEndsWith: "Ends With",
    filterEquals: "Equals",
    filterEqualsString: "Equals",
    filterFuzzy: "Fuzzy",
    filterGreaterThan: "Greater Than",
    filterGreaterThanOrEqualTo: "Greater Than Or Equal To",
    filterInNumberRange: "Between",
    filterIncludesString: "Contains",
    filterIncludesStringSensitive: "Contains",
    filterLessThan: "Less Than",
    filterLessThanOrEqualTo: "Less Than Or Equal To",
    filterMode: "Filter Mode: {filterType}",
    filterNotEmpty: "Not Empty",
    filterNotEquals: "Not Equals",
    filterStartsWith: "Starts With",
    filterWeakEquals: "Equals",
    filteringByColumn: "Filtering by {column} - {filterType} {filterValue}",
    goToFirstPage: "Go to first page",
    goToLastPage: "Go to last page",
    goToNextPage: "Go to next page",
    goToPreviousPage: "Go to previous page",
    grab: "Grab",
    groupByColumn: "Group by {column}",
    groupedBy: "Grouped by ",
    hideAll: "Hide all",
    hideColumn: "Hide {column} column",
    max: "Max",
    min: "Min",
    move: "Move",
    noRecordsToDisplay: "No records to display",
    noResultsFound: "No results found",
    of: "of",
    or: "or",
    pinToLeft: "Pin to left",
    pinToRight: "Pin to right",
    resetColumnSize: "Reset column size",
    resetOrder: "Reset order",
    rowActions: "Row Actions",
    rowNumber: "#",
    rowNumbers: "Row Numbers",
    rowsPerPage: "Rows per page",
    save: "Save",
    search: "Search",
    selectedCountOfRowCountRowsSelected:
      "{selectedCount} of {rowCount} row(s) selected",
    select: "Select",
    showAll: "Show all",
    showAllColumns: "Show all columns",
    showHideColumns: "Show/Hide columns",
    showHideFilters: "Show/Hide filters",
    showHideSearch: "Show/Hide search",
    sortByColumnAsc: "Sort by {column} ascending",
    sortByColumnDesc: "Sort by {column} descending",
    sortedByColumnAsc: "Sorted by {column} ascending",
    sortedByColumnDesc: "Sorted by {column} descending",
    thenBy: ", then by ",
    toggleDensity: "Toggle density",
    toggleFullScreen: "Toggle full screen",
    toggleSelectAll: "Toggle select all",
    toggleSelectRow: "Toggle select row",
    toggleVisibility: "Toggle visibility",
    ungroupByColumn: "Ungroup by {column}",
    unpin: "Unpin",
    unpinAll: "Unpin all",
    unsorted: "Unsorted",
    // add
    loading_error: "Error loading data",
  },

  password_change: {
    title: "Change Password",
    current_label: "Current Password",
    new_label: "New Password",
    confirm_label: "Confirm New Password",
    step_verify: "Verify Current Password",
    step_set_new: "Set New Password",
    btn_cancel: "Cancel",
    btn_verify: "Verify Password",
    btn_confirm: "Confirm Change",
    btn_processing: "Processing...",
    error_current_required: "Please enter current password",
    error_current_incorrect: "Current password is incorrect, please try again",
    error_verify_failed: "Verification failed, please try again",
    success_verified: "Password verification successful",
    error_confirm_required: "Please confirm new password",
    error_passwords_not_match: "Passwords do not match",
    error_password_weak:
      "Password strength is insufficient, please set a stronger password",
    success_changed: "Password changed successfully",
    error_change_failed: "Password change failed, please try again",
  },

  password_strength: {
    // Password strength related
    title: "Password Strength",
    none: "Please enter password",
    weak: "Weak",
    medium: "Medium",
    strong: "Strong",
    requirements_title: "Password Requirements:",
    requirement_length: "At least 12 characters",
    requirement_lowercase: "Contains lowercase letters",
    requirement_uppercase: "Contains uppercase letters",
    requirement_number: "Contains numbers",
    requirement_special: "Contains special characters",
    error_required: "Please enter password",
    error_min_length: "Password must be at least 12 characters",
    error_max_length: "Password cannot exceed 64 characters",
    error_lowercase: "Password must contain lowercase letters",
    error_uppercase: "Password must contain uppercase letters",
    error_number: "Password must contain numbers",
    error_special: "Password must contain special characters",
    error_no_spaces_emoji: "Password cannot contain spaces or emojis",
    error_same_as_current:
      "New password cannot be the same as current password",
  },

  account_delete: {
    // Account deletion related
    title: "Delete Account Confirmation",
    confirmation_question: "Are you sure you want to delete your account?",
    warning_message:
      "This action is permanent and will remove all data associated with your account from the system.",
    password_prompt: "To confirm, please enter your current password.",
    current_password_label: "Current Password",
    password_placeholder: "Enter your Current password",
    cancel_button: "Cancel",
    delete_button: "Delete Account",
    deleting_button: "Deleting...",
    error_password_required: "Please enter your current password",
    error_delete_failed: "Failed to delete account, please try again",
    success_message:
      "Account deleted successfully, you will be redirected to the login page.",
  },

  two_factor_auth: {
    // Two-factor authentication related
    title: "Two-Factor Authentication",
    what_is_2fa: "What is Two-Factor Authentication?",
    description:
      "Two-Factor Authentication (2FA) is an additional security measure that requires email verification code in addition to your password to confirm your identity, greatly improving account security and preventing unauthorized access.",
    code_sent_to: "Verification code sent to:",
    enter_code: "Please enter the 6-digit verification code",
    enter_complete_code: "Please enter the complete 6-digit verification code",
    verification_failed: "Verification failed, please try again",
    no_code_received: "Didn't receive the code?",
    resend_code: "Resend verification code",
    resend_countdown: "Resend ({{count}}s)",
    sending: "Sending...",
    code_resent_success: "Verification code has been resent",
    code_resend_failed:
      "Failed to send verification code, please try again later",
    cancel: "Cancel",
    verify: "Verify",
    verifying: "Verifying...",
    enabled: "Two-factor authentication enabled",
    disabled: "Two-factor authentication disabled",
    toggle_failed: "Failed to toggle two-factor authentication",
  },

  security: {
    // Security settings related
    two_factor_enabled: "Two-factor authentication enabled",
    two_factor_disabled: "Two-factor authentication disabled",
    two_factor_toggle_failed: "Failed to toggle two-factor authentication",
    two_factor_auth_title: "Two-Factor Authentication",
    two_factor_enabled_label: "Enabled",
    two_factor_description:
      "Add an extra layer of security to your account through email verification codes",
    two_factor_login_notice:
      "Email verification code required for secondary authentication during login",
  },

  return_original_account: {
    title: "Return Original Account",
    confirmation_question: "Are you sure you want to return original account?",
    password_prompt: "To confirm, please enter original account password.",
    password_label: "Original Account Password",
    password_placeholder: "Enter original account password",
    error_password_required: "Please enter original account password",
    error_return_failed: "Failed to return original account, please try again",
    return_button: "Return Original Account",
    returning_button: "Returning...",
  },

  data: {
    private_agree: `Personal Information Protection and Privacy Policy`,
    user_agree: "ZKDIGIMAX L3 User Agreement",
    private_agree_text: private_agree_text,
    user_agree_text: user_agree_text,
  },
};

export default en;
