/* eslint-disable react/no-children-prop */
/* eslint-disable react/jsx-no-undef */
import React from "react";
import {
  BootstrapDialog,
  BootstrapContent,
  BootstrapActions,
  BootstrapDialogTitle,
} from "@/components/dialog";
import {
  Stack,
  Grid,
  Button,
  Typography,
  InputLabel,
  FormHelperText,
  OutlinedInput,
  InputAdornment,
  InputBase,
} from "@mui/material";
import { useTranslation } from "react-i18next";
export default function UserPrivateAgree(props) {
  const { open, handleClose = () => {} } = props;
  const { t } = useTranslation();
  const content = t("data.private_agree_text");
  return (
    <>
      <BootstrapDialog
        fullWidth
        maxWidth="sm"
        onClose={handleClose}
        aria-labelledby="customized-dialog-title"
        open={open}
      >
        <BootstrapDialogTitle
          id="customized-dialog-title"
          onClose={handleClose}
        >
          <Typography variant="h4" component="p">
            {t("data.private_agree")}
          </Typography>
        </BootstrapDialogTitle>
        <BootstrapContent>
          <div
            dangerouslySetInnerHTML={{
              __html: content,
            }}
          />
        </BootstrapContent>
      </BootstrapDialog>
    </>
  );
}
