/* eslint-disable react-hooks/exhaustive-deps */
import React from "react";
import { operationlogList, operationlogDetail } from "@s/operationlog";
import { useFormik } from "formik";
import ZktecoTable from "@c/ZktecoTable";
import SearchForm from "@c/SearchForm";
import LayoutList from "@l/components/LayoutList";
import CustomInput from "@c/CustInput.jsx";
import { pxToRem } from "@u/zkUtils";
import CustomSelect from "@c/CustomSelect";
import ZkTooltip from "@c/ZkTooltip";
import useDict from "@/hooks/useDict.js";
import dayjs from "dayjs";
import { findDict } from "@/utils/zkUtils";
import OperationLogDetailModal from "./OperationLogDetailModal";
import ViewIcon from "@/assets/Icons/Viewicon.svg?react";
import DictTag from "@c/DictTag";
import { useStateUserInfo } from "@/hooks/user";
import { SUPER_ADMIN } from "@/constant/role";

const OperationList = () => {
  const { t } = useTranslation();
  const userInfo = useStateUserInfo();
  const isSuperAdmin = userInfo?.roles?.includes(SUPER_ADMIN);
  const dictData = useDict([
    "common_status",
    "site_timezone_type",
    "sys_operation_log_action",
    "sys_operator_type",
    "sys_module_code",
    "sys_app_code",
  ]);

  const [detailOpen, setDetailOpen] = useState(false);
  const [isError, setIsError] = useState(false);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);

  const [detailData, setDetailData] = useState({});

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });
  // 构建参数
  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      isAsc: "",
      orderBy: "DESC",
      startTime: "",
      endTime: "",
      keyWard: "",
    };

    return params;
  };

  // 获取数据
  const getTableData = async () => {
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }

    try {
      setIsLoading(true);
      await operationlogList(buildParams()).then((res) => {
        setData(res?.data?.data);
        setRowCount(res?.data?.total);
      });
    } finally {
      setIsError(true);
      setIsLoading(false);
      setIsRefetching(false);
    }
  };
  useEffect(() => {
    getTableData();
  }, [pagination.pageIndex, pagination.pageSize]);

  // 列字段
  const columns = useMemo(
    () => [
      // {
      //   accessorKey: "operTitle", //access nested data with dot notation
      //   header: t("operation_log.title"),
      //   Cell: ({ row }) => {
      //     return (
      //       <ZkTooltip
      //         title={t(`${row.original.operTitle}`)}
      //         arrow
      //         placement="bottom">
      //         <span>{t(`${row.original.operTitle}`)}</span>
      //       </ZkTooltip>
      //     );
      //   },
      // },
      {
        accessorKey: "operName", //access nested data with dot notation
        header: t("operation_log.account"),
        Cell: ({ row }) => {
          return (
            <ZkTooltip title={row.original.operName} arrow placement="bottom">
              <span>{row.original.operName}</span>
            </ZkTooltip>
          );
        },
      },
      {
        accessorKey: "ipAddr", //access nested data with dot notation
        header: t("operation_log.ip"),
        Cell: ({ row }) => {
          return (
            <ZkTooltip title={row.original.ipAddr} arrow placement="bottom">
              <span>{row.original.ipAddr}</span>
            </ZkTooltip>
          );
        },
      },
      {
        accessorKey: "location", //access nested data with dot notation
        header: t("operation_log.address"),
        Cell: ({ row }) => {
          return (
            <ZkTooltip title={row.original.location} arrow placement="bottom">
              <span>{row.original.location}</span>
            </ZkTooltip>
          );
        },
      },

      {
        accessorKey: "status", //access nested data with dot notation
        header: t("login_log.status"),
        enableColumnActions: false,
        enableSorting: false,
        Cell: ({ row }) => {
          return (
            <DictTag
              dicts={dictData.current?.common_status}
              value={row.original.status}
            />
          );
        },
      },
      {
        accessorKey: "operationTime", //access nested data with dot notation
        header: t("operation_log.time"),
        Cell: ({ row }) => {
          return (
            <ZkTooltip
              title={dayjs(row.original.accessTime).format(
                "YYYY-MM-DD HH:mm:ss"
              )}
              arrow
              placement="bottom"
            >
              <span>
                {dayjs(row.original.accessTime).format("YYYY-MM-DD HH:mm:ss")}
              </span>
            </ZkTooltip>
          );
        },
      },

      {
        accessorKey: "operTitle", //access nested data with dot notation
        header: t("common.common_description"),
        size: 300, // 限制列宽度
        Cell: ({ row }) => {
          const businessTypeData = findDict(
            dictData.current?.sys_operation_log_action,
            row.original.businessType
          );

          const operatorTypeData = findDict(
            dictData.current?.sys_operator_type,
            row.original.operatorType
          );
          const appCodeData = findDict(
            dictData.current?.sys_app_code,
            row.original.appCode
          );
          const moduleCodeData = findDict(
            dictData.current?.sys_module_code,
            row.original.moduleCode
          );

          const businessType = businessTypeData ? businessTypeData.label : "-";
          const operatorType = operatorTypeData ? operatorTypeData.label : "-";
          const appCode = appCodeData ? appCodeData.label : "-";
          const moduleCode = moduleCodeData ? moduleCodeData.label : "-";

          const description = t("operation_log.operation_description", {
            businessType,
            moduleCode,
            operatorType,
            appCode,
            description: t(row.original.operTitle),
          });

          return (
            <ZkTooltip title={description} arrow placement="bottom">
              <span
                style={{
                  display: "block",
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  maxWidth: "280px",
                  lineHeight: "1.5",
                }}
              >
                {description}
              </span>
            </ZkTooltip>
          );
        },
      },
    ],
    []
  );

  // 查询表单
  const queryFormik = useFormik({
    initialValues: {
      ...buildParams(),
      operName: "",
      // location: "",
      status: "",
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      setIsLoading(true);
      operationlogList(values)
        .then((res) => {
          if (res.code == "00000000") {
            setData(res?.data?.data);
            setRowCount(res?.data?.total);
          } else {
            setData([]);
            setRowCount(0);
          }
          setIsLoading(false);
          setIsRefetching(false);
        })
        .catch((err) => {
          setIsError(true);
          setIsLoading(false);
          setIsRefetching(false);
        });
    },
  });

  const handlePageChange = useCallback(
    (pageIndex) => {
      setPagination((prev) => ({ ...prev, pageIndex }));
    },
    [setPagination]
  );

  const handlePageSizeChange = useCallback(
    (pageSize) => {
      setPagination({ pageIndex: 0, pageSize });
    },
    [setPagination]
  );

  return (
    <>
      <LayoutList
        title={t("operation_log.name")}
        header={
          <SearchForm formik={queryFormik} reloadData={getTableData}>
            <Grid container>
              <Grid item ml={2}>
                <CustomInput
                  size="small"
                  type="text"
                  width={pxToRem(264)}
                  name="operName"
                  fullWidth
                  formik={queryFormik}
                  placeholder={t("operation_log.account")}
                />
              </Grid>

              {/* <Grid item ml={2}>
              <CustomInput
                size="small"
                type="text"
                name="location"
                width={pxToRem(264)}
                fullWidth
                formik={queryFormik}
                placeholder={t("operation_log.ip")}
              />
            </Grid> */}

              {/* <Grid item ml={2}>
              <CustomInput
                size="small"
                type="text"
                name="location"
                width={pxToRem(264)}
                fullWidth
                placeholder={t("operation_log.address")}
              />
            </Grid> */}

              <Grid item ml={2}>
                <CustomSelect
                  name="status"
                  items={dictData.current.common_status}
                  formik={queryFormik}
                  placeholder={t("login_log.status")}
                ></CustomSelect>
              </Grid>
            </Grid>
          </SearchForm>
        }
        content={
          // 加上userInfo判断，避免操作按钮不显示
          userInfo && (
            <ZktecoTable
              columns={columns}
              data={data}
              rowCount={rowCount}
              isLoading={isLoading}
              isRefetching={isRefetching}
              isError={isError}
              loadDada={getTableData}
              paginationProps={{
                currentPage: pagination.pageIndex,
                rowsPerPage: pagination.pageSize,
                onPageChange: handlePageChange,
                onPageSizeChange: handlePageSizeChange,
              }}
              renderRowActions={({ row }) => {
                return isSuperAdmin ? (
                  <Tooltip title={t("common.common_view")}>
                    <Button
                      onClick={async () => {
                        setDetailOpen(true);
                        const response = await operationlogDetail(
                          row.original.id
                        );
                        setDetailData(response?.data);
                      }}
                    >
                      <ViewIcon
                        style={{
                          width: "25px",
                        }}
                      ></ViewIcon>
                    </Button>
                  </Tooltip>
                ) : (
                  <></>
                );
              }}
              enableRowActions={isSuperAdmin}
              topActions={{
                showAdd: false,
              }}
              isShowAction={false}
            />
          )
        }
      ></LayoutList>

      <OperationLogDetailModal
        open={detailOpen}
        onClose={() => {
          setDetailOpen(false);
          setDetailData({});
        }}
        dictDataRef={dictData}
        logData={detailData}
      ></OperationLogDetailModal>
    </>
  );
};

export default OperationList;
