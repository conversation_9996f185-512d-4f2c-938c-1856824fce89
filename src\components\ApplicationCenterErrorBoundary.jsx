import React from 'react';
import { Box, Button, Typography, Alert, CircularProgress } from '@mui/material';
import { RefreshOutlined, HomeOutlined } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

/**
 * 应用中心专用错误边界组件
 * 提供错误恢复、自动重试和用户友好的错误提示
 */
class ApplicationCenterErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
      isRetrying: false,
      lastErrorTime: null
    };
    
    this.maxRetries = 3;
    this.retryDelay = 1000; // 1秒
    this.autoRetryDelay = 5000; // 5秒后自动重试
  }

  static getDerivedStateFromError(error) {
    // 更新 state 使下一次渲染能够显示降级后的 UI
    return { 
      hasError: true,
      lastErrorTime: Date.now()
    };
  }

  componentDidCatch(error, errorInfo) {
    // 记录错误信息
    console.error('应用中心页面错误:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // 发送错误报告（可选）
    this.reportError(error, errorInfo);
    
    // 如果重试次数未达到上限，设置自动重试
    if (this.state.retryCount < this.maxRetries) {
      setTimeout(() => {
        this.handleAutoRetry();
      }, this.autoRetryDelay);
    }
  }

  componentDidMount() {
    // 监听微前端清理事件，可能需要重新渲染
    window.addEventListener('microapp-cleanup', this.handleMicroAppCleanup);
    window.addEventListener('application-center-refresh', this.handleApplicationCenterRefresh);
  }

  componentWillUnmount() {
    window.removeEventListener('microapp-cleanup', this.handleMicroAppCleanup);
    window.removeEventListener('application-center-refresh', this.handleApplicationCenterRefresh);
  }

  handleMicroAppCleanup = () => {
    // 如果当前有错误状态，尝试恢复
    if (this.state.hasError) {
      console.log('🔄 检测到微前端清理事件，尝试恢复应用中心页面');
      this.handleRetry();
    }
  };

  handleApplicationCenterRefresh = (event) => {
    console.log('🔄 收到应用中心刷新事件:', event.detail);
    // 强制重新渲染
    this.forceUpdate();
  };

  handleRetry = () => {
    if (this.state.isRetrying) return;
    
    this.setState({ isRetrying: true });
    
    setTimeout(() => {
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        isRetrying: false,
        retryCount: this.state.retryCount + 1
      });
    }, this.retryDelay);
  };

  handleAutoRetry = () => {
    if (this.state.retryCount < this.maxRetries && this.state.hasError) {
      console.log(`🔄 自动重试应用中心页面 (${this.state.retryCount + 1}/${this.maxRetries})`);
      this.handleRetry();
    }
  };

  handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
      isRetrying: false,
      lastErrorTime: null
    });
  };

  reportError = (error, errorInfo) => {
    // 这里可以集成错误报告服务
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      retryCount: this.state.retryCount
    };
    
    console.warn('错误报告:', errorReport);
    
    // 可以发送到错误监控服务
    // errorReportingService.report(errorReport);
  };

  render() {
    if (this.state.hasError) {
      return (
        <ErrorFallbackComponent
          error={this.state.error}
          retryCount={this.state.retryCount}
          maxRetries={this.maxRetries}
          isRetrying={this.state.isRetrying}
          onRetry={this.handleRetry}
          onReset={this.handleReset}
        />
      );
    }

    return this.props.children;
  }
}

/**
 * 错误回退组件
 */
const ErrorFallbackComponent = ({ 
  error, 
  retryCount, 
  maxRetries, 
  isRetrying, 
  onRetry, 
  onReset 
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate('/');
  };

  const handleRefreshPage = () => {
    window.location.reload();
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '400px',
        padding: 4,
        textAlign: 'center'
      }}
    >
      <Alert 
        severity="error" 
        sx={{ mb: 3, maxWidth: 600 }}
      >
        <Typography variant="h6" gutterBottom>
          {t('error.applicationCenterError', '应用中心加载失败')}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {error?.message || t('error.unknownError', '未知错误')}
        </Typography>
      </Alert>

      {isRetrying ? (
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <CircularProgress size={20} sx={{ mr: 1 }} />
          <Typography variant="body2">
            {t('error.retrying', '正在重试...')} ({retryCount + 1}/{maxRetries})
          </Typography>
        </Box>
      ) : (
        <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
          {retryCount < maxRetries && (
            <Button
              variant="contained"
              startIcon={<RefreshOutlined />}
              onClick={onRetry}
              disabled={isRetrying}
            >
              {t('error.retry', '重试')} ({retryCount}/{maxRetries})
            </Button>
          )}
          
          <Button
            variant="outlined"
            startIcon={<HomeOutlined />}
            onClick={handleGoHome}
          >
            {t('error.goHome', '返回首页')}
          </Button>
          
          <Button
            variant="outlined"
            startIcon={<RefreshOutlined />}
            onClick={handleRefreshPage}
          >
            {t('error.refreshPage', '刷新页面')}
          </Button>
        </Box>
      )}

      {retryCount >= maxRetries && (
        <Alert severity="warning" sx={{ maxWidth: 600 }}>
          <Typography variant="body2">
            {t('error.maxRetriesReached', '已达到最大重试次数，请尝试刷新页面或联系技术支持。')}
          </Typography>
          <Button
            size="small"
            onClick={onReset}
            sx={{ mt: 1 }}
          >
            {t('error.resetAndRetry', '重置并重试')}
          </Button>
        </Alert>
      )}

      {process.env.NODE_ENV === 'development' && error && (
        <Box sx={{ mt: 3, maxWidth: 800, textAlign: 'left' }}>
          <Typography variant="caption" color="text.secondary">
            开发模式 - 错误详情:
          </Typography>
          <pre style={{ 
            fontSize: '12px', 
            background: '#f5f5f5', 
            padding: '8px', 
            borderRadius: '4px',
            overflow: 'auto',
            maxHeight: '200px'
          }}>
            {error.stack}
          </pre>
        </Box>
      )}
    </Box>
  );
};

export default ApplicationCenterErrorBoundary;
