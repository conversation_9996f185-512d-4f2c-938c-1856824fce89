import React, { useEffect, useState } from "react";
import RightViewLayout from "@c/layoutComponent/RightViewLayout";
import CustInput from "@c/CustInput";
import { useTranslation } from "react-i18next";
import CustomePhoneFiled from "@c/CustomePhoneFiled";
import * as Yup from "yup";
import { useFormik } from "formik";
import ZkFormik from "@c/Config/ZkFormik.jsx";
import { createValidation } from "@c/Config/validationUtils.js";
import ZKTreeSelect from "@c/zktreeselect";
import UploadImage from "@c/UploadImage";
import { RespCode } from "@/enums/RespCode";
import { handleUpload } from "./utils";
import { getFormConfig } from "./FormConfig";
import { useNavigate, useLocation } from "react-router-dom";
import { addPrincipa, editPrincipa, getPrincipaDetail } from "@s/api/principal";
import { toast } from "react-toastify";
function AddPrincipal(props) {
  const { t } = useTranslation();

  const navigate = useNavigate();
  const { state } = useLocation();
  const treeSelectRef = React.useRef(null);
  const [loading, setLoading] = React.useState(false);

  const [imageUrl, setImageUrl] = useState("");
  const [fileUrl, setFileUrl] = useState("");
  const [formConfig, setFormConfig] = useState([]);
  const [info, setInfo] = useState([]);
  useEffect(() => {
    if (state?.type === "editor" || state?.type === "view") {
      getPrincipaDetail(state?.id).then((res) => {
        setImageUrl(res?.data?.photo);
        setInfo(res?.data);
        treeSelectRef.current.setItem({
          id: res?.data?.areaId,
          name: res?.data?.areaName,
        });
      });
    }
  }, []);

  let initialValues = {};

  if (state?.type == "editor") {
    initialValues = {
      id: state?.id,
      name: info?.name,
      email: info?.email,
      countryCode: info?.countryCode,
      phone: info?.phone,
      address: info?.address,
      areaName: info?.areaName,
    };
  } else {
    initialValues = {
      name: "",
      email: "",
      countryCode: "",
      phone: "",
      areaId: "",
      address: "",
      areaName: "",
    };
  }

  // 添加表单
  const formik = useFormik({
    initialValues: initialValues,
    validationSchema: createValidation(formConfig),
    enableReinitialize: true, // 允许重新初始化
    onSubmit: (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        let parmas = {
          ...values,
          multipartFile: fileUrl ? fileUrl : null,
        };

        if (state?.type === "editor") {
          setLoading(true);
          editPrincipa(parmas).then((response) => {
            if (response?.code == RespCode.SUCCESS) {
              toast.success(response.message);
              navigate("/org/principal");
              setLoading(false);
            } else {
              navigate("/org/principal");
              toast.error(response.message);
              setLoading(false);
            }
          });
        } else {
          setLoading(true);
          addPrincipa(parmas).then((response) => {
            if (response?.code == RespCode.SUCCESS) {
              toast.success(response.message);
              navigate("/org/principal");
              setLoading(false);
            } else {
              navigate("/org/principal");
              toast.error(response.message);
              setLoading(false);
            }
          });
        }
        setLoading(false);
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setLoading(false);
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
    validationSchema: Yup.object().shape({
      name: Yup.string().required(t("common.common_area_name_not_null")),
      // phone: Yup.string().required(t("common.common_mobile_enter")),
      // longitude: Yup.string().required(
      //   t("common.common_please_area_center_location")
      // ),
      // latitude: Yup.string().required(
      //   t("common.common_please_area_center_location")
      // ),
    }),
  });

  useEffect(() => {
    const formConfig = getFormConfig(t, state?.type, treeSelectRef);
    setFormConfig(formConfig);
  }, []);

  return (
    <React.Fragment>
      <RightViewLayout
        navigateBack={"/org/principal"}
        title={
          state?.type === "editor"
            ? t("principal.edit_principal")
            : t("principal.add_principal")
        }
        handleSubmit={formik.handleSubmit}
        handleCancle={() => {
          navigate("/org/principal");
        }}
        loading={loading}>
        <ZkFormik sx={6} formik={formik} formConfig={formConfig}></ZkFormik>

        <UploadImage
          label={t("common.common_company_logo")}
          imageUrl={imageUrl}
          handleUpload={(file) => {
            handleUpload(file, setImageUrl, setFileUrl);
          }}></UploadImage>
      </RightViewLayout>
    </React.Fragment>
  );
}

export default AddPrincipal;
