import actions from "@/utils/actions";
import { Zoom } from "react-toastify";
import { createAppLifecycleHooks } from "@/utils/microAppRouteManager";
import { getStoreLang } from "@/utils/langUtils";
import { getToken } from "@/utils/auth";
import microAppNavigationHelper from "@/utils/microAppNavigationHelper";

// 环境变量配置
export const ENV_CONFIG = {
  CMS_SERVICE: import.meta.env.VITE_CMS_SERVICE,
  RETAIL_SERVICE: import.meta.env.VITE_RETAIL_SERVICE,
  EPRICE_SERVICE: import.meta.env.VITE_EPRICE_SERVICE,
};

/**
 * 确保微前端容器存在并返回容器选择器
 */
const ensureMicroAppContainer = () => {
  let container = document.getElementById('sub-app-container');

  if (!container) {
    console.log('🔧 微前端容器不存在，正在创建...');

    // 查找 MicroAppContainer 组件的容器
    const microAppWrapper = document.querySelector('[data-micro-app-wrapper]');
    let parentContainer = microAppWrapper || document.getElementById('root') || document.body;

    // 创建容器
    container = document.createElement('div');
    container.id = 'sub-app-container';
    container.style.cssText = `
      width: 100%;
      height: 100%;
      min-height: inherit;
      display: block;
      position: relative;
      opacity: 1;
      transition: opacity 0.3s ease-in-out;
    `;

    // 添加到DOM中
    parentContainer.appendChild(container);
    console.log('✅ 微前端容器已创建');
  } else {
    // 确保容器样式正确
    container.style.display = 'block';
    container.style.opacity = '1';
  }

  return container;
};


// 微前端错误处理器
export const createMicroAppErrorHandler = (appName) => (error) => {
  console.error(`${appName}加载失败:`, error);

  // 确保容器存在
  const container = ensureMicroAppContainer();

  if (container) {
    container.innerHTML = `
      <div style="padding: 20px; text-align: center; color: #f44336;">
        <h3>${appName}加载失败</h3>
        <p>错误信息: ${error.message || "未知错误"}</p>
        <p>请检查子应用是否正常运行</p>
        <button onclick="window.location.reload()" style="margin-top: 10px; padding: 8px 16px; background: #1976d2; color: white; border: none; border-radius: 4px; cursor: pointer;">
          重新加载
        </button>
      </div>
    `;
  }
  return false; // 不要跳转到500页面
};

// 微前端加载状态处理器
export const createMicroAppLoader = (appName) => (loading) => {

};

// 微前端应用配置
export const createMicroAppConfig = () => {
  // 不在这里创建容器，让 MicroAppContainer 组件负责创建
  // ensureMicroAppContainer();

  return [
    {
      name: "cms-app",
      entry: ENV_CONFIG.CMS_SERVICE,
      container: "#sub-app-container",
      activeRule: (location) => location.pathname.startsWith("/cms-app"),
      props: {
        msg: "我是来自主应用的值-react",
        actions,
        appIdentifier: "cms",
        // 传递主应用的语言和认证信息
        language: getStoreLang(),
        token: getToken(),
        // 传递主应用的国际化实例
        mainAppI18n: window.i18n,
        // 传递优化的导航辅助工具
        navigationHelper: microAppNavigationHelper,
      },
      loader: createMicroAppLoader("CMS应用"),
      errorHandler: createMicroAppErrorHandler("CMS应用"),
      sandbox: {
        strictStyleIsolation: false,
        experimentalStyleIsolation: false,
      },
      // 使用统一的路由管理生命周期钩子
      ...createAppLifecycleHooks('cms-app'),
    },
    {
      name: "retail-ai-app",
      entry: ENV_CONFIG.RETAIL_SERVICE,
      container: "#sub-app-container",
      activeRule: (location) => location.pathname.startsWith("/retail-ai-app"),
      props: {
        msg: "我是来自主应用的值-react",
        actions,
        appIdentifier: "zata",
        // 传递主应用的语言和认证信息
        language: getStoreLang(),
        token: getToken(),
        // 传递主应用的国际化实例
        mainAppI18n: window.i18n,
        // 传递优化的导航辅助工具
        navigationHelper: microAppNavigationHelper,
      },
      loader: createMicroAppLoader("零售AI应用"),
      errorHandler: createMicroAppErrorHandler("零售AI应用"),
      sandbox: {
        strictStyleIsolation: false,
        experimentalStyleIsolation: false,
      },
      // 使用统一的路由管理生命周期钩子
      ...createAppLifecycleHooks('retail-ai-app'),
    },
    {
      name: "e-price-tag-app",
      entry: ENV_CONFIG.EPRICE_SERVICE,
      container: "#sub-app-container",
      activeRule: (location) => location.pathname.startsWith("/e-price-tag-app"),
      props: {
        msg: "我是来自主应用的值-react",
        actions,
        appIdentifier: "nutag",
        // 传递主应用的语言和认证信息
        language: getStoreLang(),
        token: getToken(),
        // 传递主应用的国际化实例
        mainAppI18n: window.i18n,
        // 传递优化的导航辅助工具
        navigationHelper: microAppNavigationHelper,
      },
      loader: createMicroAppLoader("电子价签应用"),
      errorHandler: createMicroAppErrorHandler("电子价签应用"),
      sandbox: {
        strictStyleIsolation: false,
        experimentalStyleIsolation: false,
      },
      // 使用统一的路由管理生命周期钩子
      ...createAppLifecycleHooks('e-price-tag-app'),
    },
  ];
};

// qiankun启动配置
export const QIANKUN_CONFIG = {
  prefetch: "all",
  singular: true,
  sandbox: {
    strictStyleIsolation: false,
    experimentalStyleIsolation: false,
  },
};

// Toast通知配置
export const TOAST_CONFIG = {
  position: "top-center",
  style: {
    fontSize: "16px",
  },
  autoClose: 5000,
  hideProgressBar: true,
  newestOnTop: false,
  closeOnClick: true,
  rtl: false,
  limit: 5,
  pauseOnFocusLoss: true,
  draggable: true,
  pauseOnHover: true,
  theme: "light",
  transition: Zoom,
};

// 检查环境变量配置
export const checkEnvironmentConfig = () => {
  const { CMS_SERVICE, RETAIL_SERVICE, EPRICE_SERVICE } = ENV_CONFIG;

  if (!CMS_SERVICE || !RETAIL_SERVICE || !EPRICE_SERVICE) {
    // console.warn("微前端服务地址未配置，跳过注册微应用");
    // console.warn("缺失的配置:", {
    //   CMS_SERVICE: !CMS_SERVICE ? "未配置" : "已配置",
    //   RETAIL_SERVICE: !RETAIL_SERVICE ? "未配置" : "已配置",
    //   EPRICE_SERVICE: !EPRICE_SERVICE ? "未配置" : "已配置",
    // });
    return false;
  }

  return true;
};