/**
 * @Description: 页面路由容器组件
 */
import React, { useState, useEffect } from "react";
import { Navigate, useLocation, useNavigate } from "react-router-dom";
import utils from "../utils";
import Loader from "./Loader"; // Assuming a loader component exists

function Guard({ element, meta, onRouteBefore }) {
  const location = useLocation();
  const navigate = useNavigate();
  const [authorizedElement, setAuthorizedElement] = useState(null);

  useEffect(() => {
    let isMounted = true; // Flag to prevent state updates on unmounted component

    const checkAuth = async () => {
      // Set loading state immediately
      setAuthorizedElement(<Loader />);

      if (onRouteBefore) {
        try {
          const pathRes = await onRouteBefore({ pathname: location.pathname, meta });
          
          if (!isMounted) return;

          if (pathRes && pathRes !== location.pathname) {
            // If onRouteBefore returns a new path, navigate to it
            setAuthorizedElement(<Navigate to={pathRes} replace={true} />);
          } else {
            // Otherwise, the route is authorized, render the original element
            setAuthorizedElement(element);
          }
        } catch (error) {
          console.error("Error in onRouteBefore:", error);
          // Handle potential errors during auth check, maybe navigate to an error page
          if (isMounted) {
            setAuthorizedElement(<Navigate to="/500" replace={true} />);
          }
        }
      } else {
        // If no onRouteBefore is provided, just render the element
        setAuthorizedElement(element);
      }
    };

    checkAuth();

    return () => {
      isMounted = false;
    };
  }, [location.pathname, element, meta, onRouteBefore, navigate]);

  return authorizedElement;
}

export default Guard;
