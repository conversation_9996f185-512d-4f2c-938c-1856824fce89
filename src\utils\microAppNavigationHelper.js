/**
 * 微应用导航辅助工具
 * 为子应用提供优化的路由跳转方法，避免容器重新加载
 */

import { 
  getAppNameFromPath, 
  isInternalRouteChange,
  saveAppRouteState,
  notifyRouteChange
} from './microAppRouteManager';

/**
 * 优化的微应用路由跳转方法
 * 子应用可以使用这个方法进行路由跳转，避免容器重新加载
 * 
 * @param {string} targetPath - 目标路径
 * @param {Object} options - 跳转选项
 * @param {boolean} options.replace - 是否替换当前历史记录
 * @param {Object} options.state - 传递的状态对象
 * @param {Function} options.onSuccess - 跳转成功回调
 * @param {Function} options.onError - 跳转失败回调
 */
export const navigateWithinMicroApp = (targetPath, options = {}) => {
  const {
    replace = false,
    state = null,
    onSuccess = null,
    onError = null
  } = options;

  try {
    const currentPath = window.location.pathname;
    const currentAppName = getAppNameFromPath(currentPath);
    const targetAppName = getAppNameFromPath(targetPath);

    // 检查是否为有效的应用内跳转
    if (!currentAppName || !targetAppName) {
      throw new Error('无效的路径：无法识别应用名称');
    }

    if (currentAppName !== targetAppName) {
      throw new Error('跨应用跳转：请使用主应用的路由系统');
    }

    // 检查是否为应用内部跳转
    if (!isInternalRouteChange(currentPath, targetPath)) {
      throw new Error('非应用内部跳转：请使用主应用的路由系统');
    }

    console.log(`🚀 微应用内部路由跳转: ${currentPath} -> ${targetPath}`);

    // 保存路由状态
    saveAppRouteState(currentAppName, targetPath);

    // 执行路由跳转
    if (replace) {
      window.history.replaceState(state, '', targetPath);
    } else {
      window.history.pushState(state, '', targetPath);
    }

    // 通知路由变化
    notifyRouteChange(currentAppName, targetPath, currentPath);

    // 触发自定义事件，通知主应用路由已变化
    const routeChangeEvent = new CustomEvent('microapp-internal-navigation', {
      detail: {
        appName: currentAppName,
        from: currentPath,
        to: targetPath,
        replace,
        timestamp: Date.now()
      }
    });
    window.dispatchEvent(routeChangeEvent);

    // 执行成功回调
    if (onSuccess) {
      onSuccess({ from: currentPath, to: targetPath });
    }

    return true;
  } catch (error) {
    console.error('微应用路由跳转失败:', error);
    
    // 执行错误回调
    if (onError) {
      onError(error);
    }
    
    return false;
  }
};

/**
 * 获取当前微应用的路由信息
 * @returns {Object} 路由信息对象
 */
export const getCurrentMicroAppRoute = () => {
  const currentPath = window.location.pathname;
  const appName = getAppNameFromPath(currentPath);
  
  return {
    path: currentPath,
    appName,
    isValid: !!appName,
    timestamp: Date.now()
  };
};

/**
 * 检查路径是否为当前应用的有效路径
 * @param {string} path - 要检查的路径
 * @returns {boolean} 是否为有效路径
 */
export const isValidPathForCurrentApp = (path) => {
  const currentPath = window.location.pathname;
  const currentAppName = getAppNameFromPath(currentPath);
  const targetAppName = getAppNameFromPath(path);
  
  return currentAppName && targetAppName && currentAppName === targetAppName;
};

/**
 * 为子应用提供的React Hook风格的导航方法
 * 子应用可以在React组件中使用这个方法
 */
export const useMicroAppNavigation = () => {
  const navigate = (path, options = {}) => {
    return navigateWithinMicroApp(path, options);
  };

  const getCurrentRoute = () => {
    return getCurrentMicroAppRoute();
  };

  const isValidPath = (path) => {
    return isValidPathForCurrentApp(path);
  };

  return {
    navigate,
    getCurrentRoute,
    isValidPath
  };
};

/**
 * 为子应用提供的全局导航方法
 * 子应用可以通过 window.microAppNavigation 访问
 */
export const setupGlobalMicroAppNavigation = () => {
  if (typeof window !== 'undefined') {
    window.microAppNavigation = {
      navigate: navigateWithinMicroApp,
      getCurrentRoute: getCurrentMicroAppRoute,
      isValidPath: isValidPathForCurrentApp,
      useNavigation: useMicroAppNavigation
    };
    
    console.log('✅ 全局微应用导航方法已设置');
  }
};

/**
 * 监听浏览器前进后退事件，确保路由状态同步
 */
export const setupMicroAppNavigationListener = () => {
  if (typeof window !== 'undefined') {
    const handlePopState = (event) => {
      const currentPath = window.location.pathname;
      const appName = getAppNameFromPath(currentPath);
      
      if (appName) {
        console.log(`🔄 微应用浏览器导航: ${currentPath}`);
        
        // 保存路由状态
        saveAppRouteState(appName, currentPath);
        
        // 触发自定义事件
        const navigationEvent = new CustomEvent('microapp-browser-navigation', {
          detail: {
            appName,
            path: currentPath,
            state: event.state,
            timestamp: Date.now()
          }
        });
        window.dispatchEvent(navigationEvent);
      }
    };

    window.addEventListener('popstate', handlePopState);
    
    // 返回清理函数
    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }
};

// 自动设置全局导航方法
if (typeof window !== 'undefined') {
  setupGlobalMicroAppNavigation();
  setupMicroAppNavigationListener();
}

export default {
  navigateWithinMicroApp,
  getCurrentMicroAppRoute,
  isValidPathForCurrentApp,
  useMicroAppNavigation,
  setupGlobalMicroAppNavigation,
  setupMicroAppNavigationListener
};
