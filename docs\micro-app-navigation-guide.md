# 微应用路由跳转优化指南

## 问题描述

在微前端架构中，当子应用使用 React Router 的 `navigate` 方法进行路由跳转时，会导致整个 `MicroAppContainer` 重新加载，这是因为主应用的路由系统检测到路径变化后重新渲染了微应用容器。

## 解决方案

我们提供了一个优化的导航辅助工具 `microAppNavigationHelper`，子应用可以使用这个工具进行路由跳转，避免容器重新加载。

## 使用方法

### 1. 通过 props 获取导航辅助工具

子应用可以通过 props 获取主应用传递的 `navigationHelper`：

```javascript
// 在子应用的入口文件或组件中
function SubApp(props) {
  const { navigationHelper } = props;
  
  // 使用导航辅助工具
  const handleNavigate = (path) => {
    navigationHelper.navigateWithinMicroApp(path, {
      replace: false,
      onSuccess: ({ from, to }) => {
        console.log(`路由跳转成功: ${from} -> ${to}`);
      },
      onError: (error) => {
        console.error('路由跳转失败:', error);
      }
    });
  };
  
  return (
    <div>
      <button onClick={() => handleNavigate('/cms-app/list')}>
        跳转到列表页
      </button>
    </div>
  );
}
```

### 2. 使用全局方法

导航辅助工具会自动在 `window` 对象上设置全局方法：

```javascript
// 在子应用的任何地方都可以使用
window.microAppNavigation.navigate('/cms-app/list', {
  replace: false,
  onSuccess: ({ from, to }) => {
    console.log(`路由跳转成功: ${from} -> ${to}`);
  }
});

// 获取当前路由信息
const currentRoute = window.microAppNavigation.getCurrentRoute();
console.log('当前路由:', currentRoute);

// 检查路径是否有效
const isValid = window.microAppNavigation.isValidPath('/cms-app/list');
console.log('路径是否有效:', isValid);
```

### 3. 使用 React Hook 风格的方法

```javascript
import React from 'react';

function MyComponent(props) {
  const { navigationHelper } = props;
  const { navigate, getCurrentRoute, isValidPath } = navigationHelper.useNavigation();
  
  const handleNavigate = () => {
    const success = navigate('/cms-app/list', {
      replace: false,
      onSuccess: ({ from, to }) => {
        console.log(`导航成功: ${from} -> ${to}`);
      }
    });
    
    if (!success) {
      console.error('导航失败');
    }
  };
  
  return (
    <button onClick={handleNavigate}>
      跳转到列表页
    </button>
  );
}
```

## API 参考

### navigateWithinMicroApp(targetPath, options)

优化的微应用路由跳转方法。

**参数：**
- `targetPath` (string): 目标路径
- `options` (object): 跳转选项
  - `replace` (boolean): 是否替换当前历史记录，默认 false
  - `state` (object): 传递的状态对象
  - `onSuccess` (function): 跳转成功回调
  - `onError` (function): 跳转失败回调

**返回值：**
- `boolean`: 跳转是否成功

**示例：**
```javascript
const success = navigateWithinMicroApp('/cms-app/detail/123', {
  replace: true,
  state: { from: 'list' },
  onSuccess: ({ from, to }) => {
    console.log('跳转成功');
  },
  onError: (error) => {
    console.error('跳转失败:', error);
  }
});
```

### getCurrentMicroAppRoute()

获取当前微应用的路由信息。

**返回值：**
```javascript
{
  path: string,        // 当前路径
  appName: string,     // 应用名称
  isValid: boolean,    // 是否为有效路径
  timestamp: number    // 时间戳
}
```

### isValidPathForCurrentApp(path)

检查路径是否为当前应用的有效路径。

**参数：**
- `path` (string): 要检查的路径

**返回值：**
- `boolean`: 是否为有效路径

## 注意事项

1. **只能用于应用内部跳转**：这个工具只能用于同一个微应用内的路由跳转，跨应用跳转仍需要使用主应用的路由系统。

2. **路径格式**：路径必须以应用前缀开头，例如：
   - CMS应用：`/cms-app/...`
   - 零售AI应用：`/retail-ai-app/...`
   - 电子价签应用：`/e-price-tag-app/...`

3. **错误处理**：如果跳转失败（如跨应用跳转），工具会返回 false 并调用 onError 回调。

4. **浏览器兼容性**：工具使用了现代浏览器的 History API，确保在支持的浏览器中使用。

## 事件监听

工具会触发以下自定义事件，主应用和子应用都可以监听：

### microapp-internal-navigation

当子应用进行内部路由跳转时触发。

```javascript
window.addEventListener('microapp-internal-navigation', (event) => {
  const { appName, from, to, replace, timestamp } = event.detail;
  console.log(`${appName} 应用内部导航: ${from} -> ${to}`);
});
```

### microapp-browser-navigation

当用户使用浏览器前进后退按钮时触发。

```javascript
window.addEventListener('microapp-browser-navigation', (event) => {
  const { appName, path, state, timestamp } = event.detail;
  console.log(`${appName} 浏览器导航: ${path}`);
});
```

## 最佳实践

1. **优先使用优化的导航方法**：在子应用中，优先使用 `navigationHelper` 提供的方法而不是直接使用 React Router 的 navigate。

2. **添加错误处理**：始终为导航操作添加错误处理，以便在跳转失败时提供用户友好的反馈。

3. **路径验证**：在跳转前使用 `isValidPath` 方法验证路径的有效性。

4. **状态管理**：合理使用 state 参数传递页面间的状态信息。

通过使用这个优化的导航工具，子应用可以实现流畅的路由跳转，避免容器重新加载带来的性能问题和用户体验问题。
