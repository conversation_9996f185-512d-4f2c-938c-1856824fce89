import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import RightViewLayout from "@c/layoutComponent/ViewBox";
import { Avatar, Grid, Typography, Paper, Divider } from "@mui/material";
import { useLocation, useNavigate } from "react-router-dom";
import { getTenantDetail } from "@s/api/tenant";
import ViewBox from "@/components/ViewBox";
function ViewBranch(props) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { state } = useLocation();
  const [data, setData] = useState([]);

  useEffect(() => {
    if (state?.type === "view" || state?.type === "editor") {
      getTenantDetail(state?.id).then((res) => {
        setData(res?.data);
      });
    }
  }, [state]);

  let titleStyle = {
    font: `normal normal normal 14px/18px Proxima Nova`,
    color: "#474B4F",
    opacity: 0.5,
  };

  let textStyle = {
    fontSize: "16px",
    color: "#474B4F",
    marginTop: "10px",
  };

  return (
    <React.Fragment>
      <RightViewLayout
        title={t("branch.view_branch")}
        navigateBack={"/org/branch"}
        handleCancle={() => {
          navigate("/org/branch");
        }}
        isShowSave={false}>
        <Grid container flexDirection={"column"}>
          <ViewBox title={t("common.common_first_name")} content={data?.name} />

          <ViewBox title={t("partner_user.email")} content={data?.email} />

          <ViewBox
            title={t("common.common_mobile")}
            content={
              data?.countryCode && data?.phone
                ? `+ ${data?.countryCode} ${data?.phone}`
                : "-"
            }
          />

          <ViewBox
            title={t("partner.data_permission")}
            content={data?.dataPermission || "-"}
          />

          <ViewBox title={t("roles.title")} content={data?.roles || "-"} />
        </Grid>
      </RightViewLayout>
    </React.Fragment>
  );
}

export default ViewBranch;
