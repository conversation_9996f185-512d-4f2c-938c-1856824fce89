import { ORIGINAL_ACCOUNT, TENANT_CODE, TENANT_NAME } from "@/constant/session";
import { useStateUserInfo } from "@/hooks/user.js";
import { clearUser } from "@/store/reducers/user";
import { getToken, removeToken, setToken } from "@/utils/auth";
import { getStoreLang, setStoreLang } from "@/utils/langUtils";
import LayoutList from "@l/components/LayoutList";
import { getPrincipaList, switchDepartment } from "@s/api/principal";
import { jwtDecode } from "jwt-decode";
import React from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { useTableRequest } from "../../utils.js";
import TableList from "./components/TableList";

const index = () => {
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch();
  const userInfo = useStateUserInfo();

  const [serchName, setSeachName] = useState("");
  const {
    data,
    isLoading,
    isRefetching,
    isError,
    rowCount,
    pagination,
    setPagination,
    fetchData,
    search,
    reset,
  } = useTableRequest(getPrincipaList);

  // 搜索
  const handlerSeacher = () => {
    search({ name: serchName });
  };

  // 清空
  const handleClear = () => {
    setSeachName("");
    reset();
  };

  const handleSwitchDepartment = (department) => {
    switchDepartment(department).then((res) => {
      const email = jwtDecode(getToken())?.email;
      const tenantCode = sessionStorage.getItem(TENANT_CODE);
      const tenantName = sessionStorage.getItem(TENANT_NAME);
      const lang = getStoreLang();

      dispatch(clearUser());

      // 清除所有 localStorage 数据
      localStorage.clear();

      // 清除 sessionStorage 中的所有数据
      sessionStorage.clear();
      // 清除所有 cookie
      document.cookie.split(";").forEach(function (c) {
        document.cookie = c
          .replace(/^ +/, "")
          .replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
      });
      // 移除token
      removeToken();
      setToken(res.data.access_token);

      let resData = res.data;
      sessionStorage.setItem(TENANT_CODE, resData.tenantCode);
      tenantName && sessionStorage.setItem(TENANT_NAME, tenantName);
      sessionStorage.setItem("OUTLET_PRODUCT", "1");
      sessionStorage.setItem(ORIGINAL_ACCOUNT, email);
      i18n.changeLanguage(lang);
      // 当前语言保存到浏览器
      setStoreLang(lang);
      window.location.reload();
    });
  };

  const rederTable = () => {
    return (
      <TableList
        data={data}
        isLoading={isLoading}
        isRefetching={isRefetching}
        isError={isError}
        rowCount={rowCount}
        pagination={pagination}
        setPagination={setPagination}
        switchDepartment={handleSwitchDepartment}
        getTableData={fetchData}
      ></TableList>
    );
  };

  return (
    <React.Fragment>
      <LayoutList
        title={t("principal.title")}
        isSearch={true}
        onClick={handlerSeacher}
        serchName={serchName}
        setSeachName={setSeachName}
        onClear={handleClear} // 添加 onClear 属性
        content={rederTable()}
      ></LayoutList>
    </React.Fragment>
  );
};

export default index;
