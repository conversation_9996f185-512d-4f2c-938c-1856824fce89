/**
 * 微前端路由管理工具
 * 解决子应用路由状态管理问题，确保正确的路由跳转和状态重置
 */

// 微前端应用的默认首页路由配置
const APP_DEFAULT_ROUTES = {
  'cms-app': '/cms-app/dashboard/summary',
  'retail-ai-app': '/retail-ai-app/peopleCounting',
  'e-price-tag-app': '/e-price-tag-app/dashboard'
};

// 存储微前端应用的路由状态
const appRouteStates = new Map();

/**
 * 获取应用名称从路径
 * @param {string} pathname - 路径
 * @returns {string} 应用名称
 */
export const getAppNameFromPath = (pathname) => {
  if (pathname.startsWith('/cms-app')) return 'cms-app';
  if (pathname.startsWith('/retail-ai-app')) return 'retail-ai-app';
  if (pathname.startsWith('/e-price-tag-app')) return 'e-price-tag-app';
  return null;
};

/**
 * 获取应用的默认路由
 * @param {string} appName - 应用名称
 * @returns {string} 默认路由
 */
export const getAppDefaultRoute = (appName) => {
  return APP_DEFAULT_ROUTES[appName] || `/${appName}/dashboard`;
};

/**
 * 检查是否为应用根路径
 * @param {string} pathname - 路径
 * @returns {boolean} 是否为根路径
 */
export const isAppRootPath = (pathname) => {
  const appName = getAppNameFromPath(pathname);
  if (!appName) return false;

  return pathname === `/${appName}` || pathname === `/${appName}/`;
};

/**
 * 保存应用路由状态
 * @param {string} appName - 应用名称
 * @param {string} route - 路由
 */
export const saveAppRouteState = (appName, route) => {
  if (appName && route) {
    appRouteStates.set(appName, {
      route,
      timestamp: Date.now()
    });
  }
};

/**
 * 获取应用上次的路由状态
 * @param {string} appName - 应用名称
 * @returns {string|null} 上次的路由
 */
export const getAppLastRoute = (appName) => {
  const state = appRouteStates.get(appName);
  if (state) {
    // 检查状态是否过期（30分钟）
    const isExpired = Date.now() - state.timestamp > 30 * 60 * 1000;
    if (!isExpired) {
      return state.route;
    } else {
      // 清除过期状态
      appRouteStates.delete(appName);
    }
  }
  return null;
};

/**
 * 清除应用路由状态
 * @param {string} appName - 应用名称
 */
export const clearAppRouteState = (appName) => {
  appRouteStates.delete(appName);
};

/**
 * 清除所有应用路由状态
 */
export const clearAllAppRouteStates = () => {
  appRouteStates.clear();
};

/**
 * 确保微前端容器存在
 */
const ensureMicroAppContainer = () => {
  let container = document.getElementById('sub-app-container');

  if (!container) {
    console.log('🔧 微前端容器不存在，正在创建...');

    // 查找合适的父容器
    const appRoot = document.getElementById('root') || document.body;

    // 创建容器
    container = document.createElement('div');
    container.id = 'sub-app-container';
    container.style.cssText = `
      width: 100%;
      height: 100%;
      min-height: inherit;
      display: block;
      position: relative;
    `;

    // 添加到DOM中
    appRoot.appendChild(container);
    console.log('✅ 微前端容器已创建');
  }

  return container;
};

/**
 * 处理微前端应用挂载前的路由重置
 * @param {Object} app - qiankun应用实例
 * @param {string} appName - 应用名称
 */
export const handleAppBeforeMount = (app, appName) => {
  try {
    console.log(`🔄 ${appName} 应用即将挂载，检查容器和路由状态`);

    // 1. 确保微前端容器存在
    const container = ensureMicroAppContainer();
    if (!container) {
      console.error('❌ 无法创建微前端容器');
      return Promise.reject(new Error('微前端容器创建失败'));
    }

    // 2. 检查应用实例
    if (!app || !app.window) {
      return Promise.resolve();
    }

    const currentPath = window.location.pathname;
    const isRootPath = isAppRootPath(currentPath);

    // 3. 如果是根路径，重定向到默认首页
    if (isRootPath) {
      const defaultRoute = getAppDefaultRoute(appName);
      console.log(`📍 重定向到默认首页: ${defaultRoute}`);

      // 使用 replaceState 避免在历史记录中添加额外条目
      window.history.replaceState(null, '', defaultRoute);

      // 同时更新子应用的路由状态（如果子应用已经有路由系统）
      if (app.window.history) {
        app.window.history.replaceState(null, '', defaultRoute);
      }
    } else {
      // 保存当前路由状态
      saveAppRouteState(appName, currentPath);
    }

    return Promise.resolve();
  } catch (error) {
    // console.error(`❌ ${appName} 应用挂载前路由处理失败:`, error);
    return Promise.resolve();
  }
};

/**
 * 处理微前端应用挂载后的路由同步
 * @param {Object} app - qiankun应用实例
 * @param {string} appName - 应用名称
 */
export const handleAppAfterMount = (app, appName) => {
  try {
    // console.log(`✅ ${appName} 应用已挂载`);

    // 监听子应用的路由变化
    if (app && app.window && app.window.addEventListener) {
      const handlePopState = () => {
        const currentPath = window.location.pathname;
        if (currentPath.startsWith(`/${appName}`)) {
          saveAppRouteState(appName, currentPath);
        }
      };

      app.window.addEventListener('popstate', handlePopState);

      // 保存事件监听器引用，用于卸载时清理
      if (!app._routeListeners) {
        app._routeListeners = [];
      }
      app._routeListeners.push({
        type: 'popstate',
        handler: handlePopState
      });
    }

    return Promise.resolve();
  } catch (error) {
    // console.error(`❌ ${appName} 应用挂载后路由处理失败:`, error);
    return Promise.resolve();
  }
};

/**
 * 处理微前端应用卸载前的清理
 * @param {Object} app - qiankun应用实例
 * @param {string} appName - 应用名称
 */
export const handleAppBeforeUnmount = (app, appName) => {
  try {
    // console.log(`🔄 ${appName} 应用即将卸载`);

    // 保存当前路由状态
    const currentPath = window.location.pathname;
    if (currentPath.startsWith(`/${appName}`)) {
      saveAppRouteState(appName, currentPath);
    }

    return Promise.resolve();
  } catch (error) {
    // console.error(`❌ ${appName} 应用卸载前处理失败:`, error);
    return Promise.resolve();
  }
};

/**
 * 处理微前端应用卸载后的清理
 * @param {Object} app - qiankun应用实例
 * @param {string} appName - 应用名称
 */
export const handleAppAfterUnmount = (app, appName) => {
  try {
    // console.log(`✅ ${appName} 应用已卸载`);

    // 清理事件监听器
    if (app && app._routeListeners) {
      app._routeListeners.forEach(({ type, handler }) => {
        if (app.window && app.window.removeEventListener) {
          app.window.removeEventListener(type, handler);
        }
      });
      app._routeListeners = [];
    }

    return Promise.resolve();
  } catch (error) {
    // console.error(`❌ ${appName} 应用卸载后处理失败:`, error);
    return Promise.resolve();
  }
};

/**
 * 创建微前端应用生命周期钩子
 * @param {string} appName - 应用名称
 * @returns {Object} 生命周期钩子对象
 */
export const createAppLifecycleHooks = (appName) => {
  return {
    beforeMount: (app) => handleAppBeforeMount(app, appName),
    afterMount: (app) => handleAppAfterMount(app, appName),
    beforeUnmount: (app) => handleAppBeforeUnmount(app, appName),
    afterUnmount: (app) => handleAppAfterUnmount(app, appName),
  };
};

/**
 * 强制重置应用到首页
 * @param {string} appName - 应用名称
 */
export const forceResetAppToHome = (appName) => {
  const defaultRoute = getAppDefaultRoute(appName);
  window.history.replaceState(null, '', defaultRoute);
  clearAppRouteState(appName);
};

// 导出默认对象
export default {
  getAppNameFromPath,
  getAppDefaultRoute,
  isAppRootPath,
  saveAppRouteState,
  getAppLastRoute,
  clearAppRouteState,
  clearAllAppRouteStates,
  createAppLifecycleHooks,
  forceResetAppToHome,
  handleAppBeforeMount,
  handleAppAfterMount,
  handleAppBeforeUnmount,
  handleAppAfterUnmount
};
