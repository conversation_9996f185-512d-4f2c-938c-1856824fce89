import React from "react";
import { useTranslation } from "react-i18next";
import RightViewLayout from "@c/layoutComponent/ViewBox";
import { getTenantUserDetail } from "@s/api/tenant";
import ViewBox from "@/components/ViewBox";
import { useLocation, useNavigate } from "react-router-dom";
function ViewEmplyee(props) {
  // 国际化
  const { t } = useTranslation();
  const { state } = useLocation();
  const navigate = useNavigate();
  const [data, setData] = useState([]);
  useEffect(() => {
    if (state?.type == "view" || state?.type == "editor") {
      getTenantUserDetail(state?.id).then((res) => {
        setData(res?.data);
      });
    }
  }, []);

  return (
    <React.Fragment>
      <RightViewLayout
        title={t("branch_user.view_branch")}
        navigateBack={"/org/branch/employee/list"}
        handleCancle={() => {
          navigate("/org/branch/employee/list");
        }}
        isShowSave={false}>
        <Grid container flexDirection={"column"} gap={4}>
          <Avatar
            className="avatar radial-button"
            alt="ZK"
            src={data?.photo}
            sx={{
              width: "130px",
              height: "130px",
            }}></Avatar>

          <ViewBox
            title={t("branch_user.firstName")}
            content={data?.firstName}
          />

          <ViewBox title={t("branch_user.lastName")} content={data?.lastName} />

          <ViewBox title={t("partner_user.email")} content={data?.email} />

          <ViewBox
            title={t("common.common_mobile")}
            content={
              data?.countryCode && data?.phone
                ? `+ ${data?.countryCode} ${data?.phone}`
                : "-"
            }
          />

          <ViewBox title={t("branch_user.address")} content={data?.address} />
        </Grid>
      </RightViewLayout>
    </React.Fragment>
  );
}

export default ViewEmplyee;
