const errorRoutes = [
  {
    path: "/404",
    component: () => import("@/router/ExceptionComponent/NotFound"),
    meta: {
      title: "页面未找到",
      i18n: "page_not_found",
      needLogin: false,
      hideLayout: true, // 标记隐藏布局
    },
  },
  {
    path: "/403",
    component: () => import("@/router/ExceptionComponent/PermissionError"),
    meta: {
      title: "访问被拒绝",
      i18n: "permission_denied",
      needLogin: false,
      hideLayout: true,
    },
  },
  {
    path: "/500",
    component: () => import("@/router/ExceptionComponent/ServerError"),
    meta: {
      title: "服务器错误",
      i18n: "server_error",
      needLogin: false,
      hideLayout: true,
    },
  },
];

export default errorRoutes;
