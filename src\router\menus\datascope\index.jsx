// 看板
const dataScopeRoutes = [
  {
    path: "/datascope",
    component: () => import("@p/dataPermission/index"),
    meta: {
      title: "Data Permission",
      i18n: "data_permission",
      authCode: "auth:datascope:query",
    },
  },

  {
    path: "/datascope/add",
    component: () => import("@/pages/dataPermission/AddDataPermission"),
    meta: {
      title: "Add Data Permission",
      i18n: "add_data_permission",
      authCode: "auth:datascope:save",
    },
  },
];
export default dataScopeRoutes;
