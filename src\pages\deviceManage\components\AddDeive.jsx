import React, { useEffect, useState } from "react";
import RightViewLayout from "@c/layoutComponent/RightViewLayout";
import { useTranslation } from "react-i18next";
import { useNavigate, useLocation } from "react-router-dom";
import CustomInput from "@/components/CustInput.jsx";
import DialogDevice from "./DialogDevice";
import { getDevicePerInfo, getDeviceDetail } from "@s/api/device.js";

import CMSDevice from "./CMSDevice";
function AddDeive(props) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { state } = useLocation();
  const [loading, setLoading] = React.useState(false);
  const [open, setOpen] = useState(false);
  const [cmsOpen, setCmsOpen] = useState(false);
  const [deviceSn, setDeviceSn] = useState(null);
  const [preDeviceInfo, setPreDeviceInfo] = useState([]);
  const [selectSence, setSelectSence] = useState("0");

  const handleSubmit = () => {
    let params = {
      // category: state?.applicationCode,
      mainOrSub: selectSence,
    };
    setLoading(true);
    getDevicePerInfo(deviceSn, params)
      .then((res) => {
        state.category == "SD" ? setCmsOpen(true) : setOpen(true);
        setPreDeviceInfo(res?.data);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <React.Fragment>
      <RightViewLayout
        navigateBack={"/device/list"}
        title={
          state?.type === "editor"
            ? t("device.edit_title")
            : t("device.add_title")
        }
        handleSubmit={handleSubmit}
        handleCancle={() => {
          navigate("/device/list");
        }}
        text={t("device.add")}
        loading={loading}>
        <Grid container xs={12}>
          <Grid item xs={4}>
            <CustomInput
              name="name"
              placeholder={t("device.device_number")}
              label={t("device.sn")}
              value={deviceSn}
              disabled={state?.type == "editor"}
              required
              handleChange={(e) => setDeviceSn(e.target.value)}></CustomInput>
          </Grid>

          {state?.category == "ZT" && (
            <Grid ml={5} mt={3}>
              <RadioGroup
                row
                value={selectSence}
                onChange={(e) => setSelectSence(e.target.value)}>
                <FormControlLabel
                  value="1"
                  control={<Radio />}
                  disabled={state?.type == "editor"}
                  label={t("device.main_device")}
                />
                <FormControlLabel
                  value="0"
                  control={<Radio />}
                  disabled={state?.type == "editor"}
                  label={t("device.other_device")}
                />
              </RadioGroup>
              <Typography
                fontSize={14}
                sx={{ color: "#bebfc0", marginTop: "-1vh" }}>
                {t("device.node_test")}
              </Typography>
            </Grid>
          )}
        </Grid>
      </RightViewLayout>

      <DialogDevice
        open={open}
        setOpen={setOpen}
        selectSence={selectSence}
        preDeviceInfo={preDeviceInfo}
        category={state?.category}
        applicationCode={state?.applicationCode}></DialogDevice>

      <CMSDevice
        open={cmsOpen}
        setOpen={setCmsOpen}
        selectSence={selectSence}
        preDeviceInfo={preDeviceInfo}
        category={state?.category}
        applicationCode={state?.applicationCode}></CMSDevice>
    </React.Fragment>
  );
}

export default AddDeive;
