// 看板
const messageLogRoute = [
  {
    path: "/profile/viewCompany",
    component: () => import("@l/MenuItem/ViewCompany"),
    meta: {
      title: "ViewCompany",
      i18n: "view_company",
      needLogin: true,
    },
  },

  {
    path: "/profile/infor",
    component: () => import("@l/MenuItem/UserProfileMenu"),
    meta: {
      title: "UserProfileMenu",
      i18n: "userProfile_menu",
      needLogin: true,
    },
  },

  {
    path: "/profile/mySubsctiption",
    component: () => import("@l/MenuItem/MySubscription"),
    meta: {
      title: "MySubscription",
      i18n: "MySubscription",
      needLogin: true,
    },
  },
];
export default messageLogRoute;
