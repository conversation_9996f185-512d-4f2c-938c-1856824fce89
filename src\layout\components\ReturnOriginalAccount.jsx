import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogT<PERSON>le,
  DialogContent,
  DialogActions,
  Typography,
  TextField,
  Button,
  Box,
  IconButton,
  InputAdornment,
} from "@mui/material";
import { Visibility, VisibilityOff, Close } from "@mui/icons-material";
import { useTranslation } from "react-i18next";
import { returnOriginalAccount } from "@/service/api/user.js";
import { useDispatch } from "react-redux";
import { removeToken, setToken } from "@/utils/auth";
import { clearUser } from "@/store/reducers/user";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { ORIGINAL_ACCOUNT, TENANT_CODE, TENANT_NAME } from "@/constant/session";
import { setStoreLang, getStoreLang } from "@/utils/langUtils";

const ReturnOriginalAccount = ({ open, setOpen }) => {
  const { t, i18n } = useTranslation();
  const [password, setPassword] = useState("");
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);

  const handlePasswordChange = (event) => {
    setPassword(event.target.value);
    if (error) setError(""); // 清除错误信息
  };

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleCancel = () => {
    setPassword("");
    setError("");
    setOpen(false);
  };

  const handleConfirm = async () => {
    if (!password.trim()) {
      setError(t("return_original_account.error_password_required"));
      return;
    }

    setLoading(true);
    try {
      // 调用删除账户的回调函数
      const res = await returnOriginalAccount({
        username: sessionStorage.getItem(ORIGINAL_ACCOUNT),
        password: password,
      });
      setError("");

      const tenantCode = sessionStorage.getItem(TENANT_CODE);
      const tenantName = sessionStorage.getItem(TENANT_NAME);
      const lang = getStoreLang();

      dispatch(clearUser());

      // 清除所有 localStorage 数据
      localStorage.clear();

      // 清除 sessionStorage 中的所有数据
      sessionStorage.clear();
      // 清除所有 cookie
      document.cookie.split(";").forEach(function (c) {
        document.cookie = c
          .replace(/^ +/, "")
          .replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
      });
      // 移除token
      removeToken();

      let resData = res.data;
      setToken(resData.access_token);

      sessionStorage.setItem(TENANT_CODE, tenantCode);
      tenantName && sessionStorage.setItem(TENANT_NAME, tenantName);
      sessionStorage.setItem("OUTLET_PRODUCT", "1");
      i18n.changeLanguage(lang);
      // 当前语言保存到浏览器
      setStoreLang(lang);
      window.location.reload();
    } catch (err) {
      setError(err.message || t("return_original_account.error_return_failed"));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={() => setOpen(false)}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          padding: 1,
          width: "580px",
        },
      }}
    >
      {/* 标题栏 */}
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          pb: 1,
          fontSize: "18px",
          fontWeight: 600,
          color: "#333",
        }}
      >
        {t("return_original_account.title")}
        <IconButton
          onClick={handleCancel}
          size="small"
          sx={{
            color: "#999",
            "&:hover": {
              color: "#666",
            },
          }}
        >
          <Close />
        </IconButton>
      </DialogTitle>

      {/* 内容区域 */}
      <DialogContent sx={{ pt: 1, pb: 3, mt: 3 }}>
        <Box>
          {/* 确认问题 */}
          <Typography
            variant="body1"
            sx={{
              mb: 2,
              color: "#333",
              fontWeight: 500,
              fontSize: "14px",
            }}
          >
            {t("return_original_account.confirmation_question")}
          </Typography>

          {/* 密码输入提示 */}
          <Typography
            variant="body2"
            sx={{
              mb: 2,
              color: "#333",
              fontWeight: 500,
              fontSize: "14px",
            }}
          >
            {t("return_original_account.password_prompt")}
          </Typography>

          {/* 密码输入框 */}
          <TextField
            fullWidth
            label={t("return_original_account.password_label")}
            type={showPassword ? "text" : "password"}
            value={password}
            onChange={handlePasswordChange}
            error={!!error}
            helperText={error}
            disabled={loading}
            placeholder={t("return_original_account.password_placeholder")}
            sx={{
              "& .MuiOutlinedInput-root": {
                borderRadius: 1,
              },
            }}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={handleTogglePasswordVisibility}
                    edge="end"
                    disabled={loading}
                    sx={{ color: "#999" }}
                  >
                    {showPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Box>
      </DialogContent>

      {/* 按钮区域 */}
      <DialogActions
        sx={{
          px: 3,
          pb: 3,
          pt: 0,
          gap: 2,
        }}
      >
        <Button
          onClick={() => setOpen(false)}
          variant="outlined"
          disabled={loading}
          sx={{
            minWidth: 100,
            borderRadius: 1,
            textTransform: "none",
            borderColor: "#ddd",
            color: "#666",
            "&:hover": {
              borderColor: "#bbb",
              backgroundColor: "#f5f5f5",
            },
          }}
        >
          {t("common.common_edit_cancel")}
        </Button>
        <Button
          onClick={handleConfirm}
          variant="contained"
          disabled={loading || !password.trim()}
          sx={{
            minWidth: 120,
            borderRadius: 1,
            textTransform: "none",
            backgroundColor: "#f44336",
            "&:hover": {
              backgroundColor: "#d32f2f",
            },
            "&:disabled": {
              backgroundColor: "#ffcdd2",
              color: "#fff",
            },
          }}
        >
          {loading
            ? t("return_original_account.returning_button")
            : t("return_original_account.return_button")}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ReturnOriginalAccount;
