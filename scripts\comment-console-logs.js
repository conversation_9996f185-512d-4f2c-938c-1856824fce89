/**
 * 批量注释console.log的脚本
 * 用于减少控制台打印
 */

const fs = require('fs');
const path = require('path');

// 需要处理的文件扩展名
const FILE_EXTENSIONS = ['.js', '.jsx', '.ts', '.tsx'];

// 需要排除的目录
const EXCLUDE_DIRS = ['node_modules', 'dist', 'build', '.git', 'scripts'];

// 需要排除的文件
const EXCLUDE_FILES = ['comment-console-logs.js'];

/**
 * 递归获取所有需要处理的文件
 */
function getAllFiles(dirPath, arrayOfFiles = []) {
  const files = fs.readdirSync(dirPath);

  files.forEach(file => {
    const fullPath = path.join(dirPath, file);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory()) {
      // 跳过排除的目录
      if (!EXCLUDE_DIRS.includes(file)) {
        arrayOfFiles = getAllFiles(fullPath, arrayOfFiles);
      }
    } else {
      // 检查文件扩展名和排除列表
      const ext = path.extname(file);
      if (FILE_EXTENSIONS.includes(ext) && !EXCLUDE_FILES.includes(file)) {
        arrayOfFiles.push(fullPath);
      }
    }
  });

  return arrayOfFiles;
}

/**
 * 处理单个文件，注释console.log
 */
function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 匹配各种console.log模式
    const patterns = [
      // 单行console.log
      /^(\s*)(console\.log\(.*?\);?)$/gm,
      /^(\s*)(console\.warn\(.*?\);?)$/gm,
      /^(\s*)(console\.error\(.*?\);?)$/gm,
      /^(\s*)(console\.info\(.*?\);?)$/gm,
      /^(\s*)(console\.debug\(.*?\);?)$/gm,
      
      // 多行console.log (简单情况)
      /^(\s*)(console\.log\([^;]*?\n[^;]*?\);?)$/gm,
      /^(\s*)(console\.warn\([^;]*?\n[^;]*?\);?)$/gm,
      /^(\s*)(console\.error\([^;]*?\n[^;]*?\);?)$/gm,
    ];
    
    let newContent = content;
    
    patterns.forEach(pattern => {
      const matches = newContent.match(pattern);
      if (matches) {
        newContent = newContent.replace(pattern, (match, indent, consoleStatement) => {
          modified = true;
          return `${indent}// ${consoleStatement}`;
        });
      }
    });
    
    // 处理console.group和console.groupEnd
    newContent = newContent.replace(/^(\s*)(console\.group\(.*?\);?)$/gm, (match, indent, statement) => {
      modified = true;
      return `${indent}// ${statement}`;
    });
    
    newContent = newContent.replace(/^(\s*)(console\.groupEnd\(\);?)$/gm, (match, indent, statement) => {
      modified = true;
      return `${indent}// ${statement}`;
    });
    
    if (modified) {
      fs.writeFileSync(filePath, newContent, 'utf8');
      console.log(`✅ 已处理: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ 处理文件失败: ${filePath}`, error.message);
    return false;
  }
}

/**
 * 主函数
 */
function main() {
  const srcPath = path.join(__dirname, '../src');
  
  if (!fs.existsSync(srcPath)) {
    console.error('❌ src目录不存在');
    return;
  }
  
  console.log('🚀 开始批量注释console.log...');
  
  const files = getAllFiles(srcPath);
  let processedCount = 0;
  let modifiedCount = 0;
  
  files.forEach(file => {
    processedCount++;
    const wasModified = processFile(file);
    if (wasModified) {
      modifiedCount++;
    }
  });
  
  console.log(`\n📊 处理完成:`);
  console.log(`   总文件数: ${processedCount}`);
  console.log(`   修改文件数: ${modifiedCount}`);
  console.log(`   跳过文件数: ${processedCount - modifiedCount}`);
  
  if (modifiedCount > 0) {
    console.log('\n💡 提示: 已将console.log语句注释掉，如需恢复可以手动取消注释');
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  getAllFiles,
  processFile,
  main
};
