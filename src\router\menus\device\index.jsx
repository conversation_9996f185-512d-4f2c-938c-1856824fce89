// 看板
const dataPermissionRoute = [
  {
    path: "/device/list",
    component: () => import("@p/deviceManage/index"),
    meta: {
      title: "Device Managerment",
      i18n: "device_manager",
      authCode: ["dev:device:list","dev:device:save"],
    },
  },

  {
    path: "/device/add",
    component: () => import("@p/deviceManage/components/AddDeive"),
    meta: {
      title: "Add Device",
      i18n: "add_device_manager",
      authCode: "dev:device:save",
    },
  },
  {
    path: "/device/view",
    component: () => import("@p/deviceManage/components/ViewDevice"),
    meta: {
      title: "View Device",
      i18n: "view_device_manager",
      authCode: "dev:device:query",
    },
  },
];
export default dataPermissionRoute;
