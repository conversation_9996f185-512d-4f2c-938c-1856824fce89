const branchRoutes = [
  {
    path: "/org/branch",
    component: () => import("@p/Organization/branch/Company/index"),
    meta: {
      title: "Branch",
      i18n: "branch",
      authCode: "org:branch:query",
    },
  },
  {
    path: "/org/branch/edit",
    component: () =>
      import("@p/Organization/branch/Company/components/AddBranch"),
    meta: {
      title: "新增部门",
      i18n: "editor_branch",
      authCode: "org:branch:save",
    },
  },
  {
    path: "/org/branch/view",
    component: () =>
      import("@p/Organization/branch/Company/components/ViewBranch"),
    meta: {
      title: "预览部门",
      i18n: "view_branch",
      authCode: "org:branch:query",
    },
  },
  {
    path: "/org/branch/employee/list",
    component: () => import("@p/Organization/branch/Employee/index"),
    meta: {
      title: "Employee List",
      i18n: "branch_user_list",
      authCode: "org:tenant_employee:list",
    },
  },

  {
    path: "/org/branch/employee/add",
    component: () =>
      import("@p/Organization/branch/Employee/components/AddEmployee"),
    meta: {
      title: "Add Employee",
      i18n: "branch_user_add",
      authCode: "org:tenant_employee:save",
    },
  },
  {
    path: "/org/branch/employee/view",
    component: () =>
      import("@p/Organization/branch/Employee/components/ViewEmployee"),
    meta: {
      title: "View Employee",
      i18n: "branch_user_view",
      authCode: "org:tenant_employee:query",
    },
  },
];

export default branchRoutes;
