import React from "react";
import {
  Box,
  Typography,
  Card,
  CardContent,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from "@mui/material";
import {
  CheckCircle,
  Security,
  Person,
  Email,
  Phone,
  Shield,
} from "@mui/icons-material";

/**
 * 用户配置组件使用示例和说明
 */
const UserProfileExample = () => {
  return (
    <Box sx={{ p: 3, maxWidth: 800, mx: "auto" }}>
      <Typography variant="h4" gutterBottom fontWeight="600">
        用户配置组件优化说明
      </Typography>

      {/* 优化亮点 */}
      <Card sx={{ mb: 3, borderRadius: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom color="primary">
            🎉 优化亮点
          </Typography>
          
          <List>
            <ListItem>
              <ListItemIcon>
                <CheckCircle color="success" />
              </ListItemIcon>
              <ListItemText
                primary="代码结构优化"
                secondary="使用 useCallback 优化性能，代码更加简洁易读"
              />
            </ListItem>
            
            <ListItem>
              <ListItemIcon>
                <Shield color="primary" />
              </ListItemIcon>
              <ListItemText
                primary="双因子登录功能"
                secondary="新增邮箱验证码双因子登录开关，提升账户安全性"
              />
            </ListItem>
            
            <ListItem>
              <ListItemIcon>
                <Person color="info" />
              </ListItemIcon>
              <ListItemText
                primary="UI/UX 改进"
                secondary="使用 Material-UI Card 组件，界面更加现代化和一致"
              />
            </ListItem>
          </List>
        </CardContent>
      </Card>

      {/* 双因子登录说明 */}
      <Card sx={{ mb: 3, borderRadius: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom color="primary">
            🔐 双因子登录功能
          </Typography>
          
          <Alert severity="info" sx={{ mb: 2 }}>
            双因子身份验证为您的账户提供额外的安全保护层
          </Alert>

          <Typography variant="body1" paragraph>
            <strong>工作原理：</strong>
          </Typography>
          
          <List dense>
            <ListItem>
              <ListItemText
                primary="1. 用户输入用户名和密码"
                secondary="完成第一层身份验证"
              />
            </ListItem>
            <ListItem>
              <ListItemText
                primary="2. 系统发送验证码到注册邮箱"
                secondary="通过邮箱验证用户身份"
              />
            </ListItem>
            <ListItem>
              <ListItemText
                primary="3. 用户输入邮箱验证码"
                secondary="完成第二层身份验证"
              />
            </ListItem>
            <ListItem>
              <ListItemText
                primary="4. 验证成功后登录系统"
                secondary="确保账户安全"
              />
            </ListItem>
          </List>
        </CardContent>
      </Card>

      {/* 组件特性 */}
      <Card sx={{ mb: 3, borderRadius: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom color="primary">
            ⚡ 组件特性
          </Typography>

          <Box display="flex" flexDirection="column" gap={2}>
            <Box display="flex" alignItems="center" gap={2}>
              <Security color="action" />
              <Box>
                <Typography variant="subtitle2" fontWeight="500">
                  安全性增强
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  双因子登录开关，密码修改功能
                </Typography>
              </Box>
            </Box>

            <Box display="flex" alignItems="center" gap={2}>
              <Person color="action" />
              <Box>
                <Typography variant="subtitle2" fontWeight="500">
                  用户体验优化
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  清晰的视觉层次，直观的操作界面
                </Typography>
              </Box>
            </Box>

            <Box display="flex" alignItems="center" gap={2}>
              <Email color="action" />
              <Box>
                <Typography variant="subtitle2" fontWeight="500">
                  响应式设计
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  适配不同屏幕尺寸，移动端友好
                </Typography>
              </Box>
            </Box>

            <Box display="flex" alignItems="center" gap={2}>
              <Phone color="action" />
              <Box>
                <Typography variant="subtitle2" fontWeight="500">
                  国际化支持
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  完整的多语言支持，易于本地化
                </Typography>
              </Box>
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <Card sx={{ borderRadius: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom color="primary">
            📖 使用说明
          </Typography>

          <Typography variant="body1" paragraph>
            <strong>1. 基本信息编辑：</strong>
          </Typography>
          <Typography variant="body2" paragraph color="text.secondary">
            用户可以修改姓名信息，邮箱和手机号为只读状态（出于安全考虑）
          </Typography>

          <Typography variant="body1" paragraph>
            <strong>2. 头像上传：</strong>
          </Typography>
          <Typography variant="body2" paragraph color="text.secondary">
            支持头像上传和删除，实时预览功能
          </Typography>

          <Typography variant="body1" paragraph>
            <strong>3. 安全设置：</strong>
          </Typography>
          <Typography variant="body2" paragraph color="text.secondary">
            • 密码修改：点击按钮打开密码修改对话框<br/>
            • 双因子登录：通过开关启用/禁用邮箱验证码登录
          </Typography>

          <Typography variant="body1" paragraph>
            <strong>4. 数据保存：</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary">
            点击保存按钮提交所有修改，系统会验证数据完整性
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default UserProfileExample;
