import React from "react";
import { useTranslation } from "react-i18next";
import RightViewLayout from "@c/layoutComponent/RightViewLayout";
import ViewBox from "@/components/ViewBox";
import { getPrincipaDetail } from "@s/api/principal";
import { useNavigate } from "react-router-dom";
import { Avatar } from "@mui/material";
function ViewPrincipal() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [data, setData] = useState([]);
  const { state } = useLocation();
  useEffect(() => {
    getPrincipaDetail(state?.id).then((res) => {
      setData(res?.data);
    });
  }, []);

  return (
    <React.Fragment>
      <RightViewLayout
        title={t("principal.view_principal")}
        navigateBack={"/org/principal"}
        isShowSave={false}
        handleCancle={() => {
          navigate("/org/principal");
        }}>
        <Grid container flexDirection={"column"} gap={4}>
          <Avatar
            className="avatar radial-button"
            alt="ZK"
            src={data?.photo}
            sx={{
              width: "130px",
              height: "130px",
            }}></Avatar>

          <ViewBox title={t("principal.principal_name")} content={data?.name} />

          <ViewBox
            title={t("principal.organization_owner_email")}
            content={data?.email}
          />

          <ViewBox
            title={t("common.common_mobile")}
            content={
              data?.countryCode && data?.phone
                ? `+ ${data?.countryCode} ${data?.phone}`
                : "-"
            }
          />

          <ViewBox title={t("principal.region")} content={data?.areaName} />

          <ViewBox title={t("branch_user.address")} content={data?.address} />
        </Grid>
      </RightViewLayout>
    </React.Fragment>
  );
}

export default ViewPrincipal;
