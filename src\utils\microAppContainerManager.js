/**
 * 微前端容器管理器
 * 确保 #sub-app-container 在任何时候都存在
 */

let globalContainer = null;
let containerObserver = null;

/**
 * 创建微前端容器
 */
const createMicroAppContainer = () => {
  console.log('🔧 创建微前端容器...');

  const container = document.createElement('div');
  container.id = 'sub-app-container';
  container.style.cssText = `
    width: 100%;
    height: 100%;
    min-height: inherit;
    display: block;
    position: relative;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    z-index: 1;
  `;

  // 查找合适的父容器
  const microAppWrapper = document.querySelector('[data-micro-app-wrapper="true"]');
  const appRoot = document.getElementById('root');
  const parentContainer = microAppWrapper || appRoot || document.body;

  // 添加到DOM中
  parentContainer.appendChild(container);
  globalContainer = container;

  console.log('✅ 微前端容器已创建，父容器:', parentContainer.tagName, parentContainer.id || parentContainer.className);

  return container;
};

/**
 * 确保微前端容器存在
 */
export const ensureMicroAppContainer = () => {
  let container = document.getElementById('sub-app-container');

  if (!container) {
    container = createMicroAppContainer();
  } else if (container !== globalContainer) {
    // 如果找到的容器不是我们管理的全局容器，更新引用
    globalContainer = container;
  }

  // 确保容器样式正确
  if (container) {
    container.style.display = 'block';
    container.style.opacity = '1';
  }

  return container;
};

/**
 * 清理微前端容器内容（但保留容器本身）
 */
export const cleanupMicroAppContainer = (forceReload = false) => {
  const container = document.getElementById('sub-app-container');

  if (container) {
    // 清空内容
    while (container.firstChild) {
      container.removeChild(container.firstChild);
    }
    container.innerHTML = '';

    // 重置样式
    container.style.cssText = `
      width: 100%;
      height: 100%;
      min-height: inherit;
      display: block;
      position: relative;
      opacity: 1;
      transition: opacity 0.3s ease-in-out;
      z-index: 1;
    `;

    // 只有在明确要求强制重载时才刷新页面
    if (forceReload) {
      window.location.reload();
    }

    console.log('✅ 微前端容器内容已清理');
  }
};

/**
 * 监听容器变化，确保容器始终存在
 */
const startContainerObserver = () => {
  if (containerObserver) {
    return; // 已经在监听
  }

  containerObserver = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        // 检查容器是否被移除
        const container = document.getElementById('sub-app-container');
        if (!container && globalContainer && !document.contains(globalContainer)) {
          console.log('⚠️ 检测到微前端容器被移除，重新创建...');
          ensureMicroAppContainer();
        }
      }
    });
  });

  // 监听整个文档的变化
  containerObserver.observe(document.body, {
    childList: true,
    subtree: true
  });

  console.log('👀 开始监听微前端容器变化');
};

/**
 * 停止监听容器变化
 */
const stopContainerObserver = () => {
  if (containerObserver) {
    containerObserver.disconnect();
    containerObserver = null;
    console.log('🛑 停止监听微前端容器变化');
  }
};

/**
 * 初始化容器管理器
 */
export const initMicroAppContainerManager = () => {
  console.log('🚀 初始化微前端容器管理器');

  // 确保容器存在
  ensureMicroAppContainer();

  // 开始监听
  startContainerObserver();

  // 监听页面卸载，清理资源
  window.addEventListener('beforeunload', () => {
    stopContainerObserver();
  });
};

/**
 * 销毁容器管理器
 */
export const destroyMicroAppContainerManager = () => {
  stopContainerObserver();
  globalContainer = null;
  console.log('🗑️ 微前端容器管理器已销毁');
};

// 导出默认对象
export default {
  ensureMicroAppContainer,
  cleanupMicroAppContainer,
  initMicroAppContainerManager,
  destroyMicroAppContainerManager
};
