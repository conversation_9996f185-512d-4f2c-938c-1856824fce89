const retailRoute = [
  {
    path: "/retail/list",
    component: () => import("@p/RetailMainData/index"),
    meta: {
      title: "Alfamart",
      i18n: "retail_list",
      authCode: ["org:outlet:list","system:product:list","system:product:save","org:outlet:save"],
    },
  },

  {
    path: "/add/retail/outlet",
    component: () => import("@p/RetailMainData/Outlet/AddOutlet"),
    meta: {
      title: "Outlet",
      i18n: "outlet_add",
      authCode: "org:outlet:save",
    },
  },

  {
    path: "/view/retail/outlet",
    component: () => import("@p/RetailMainData/Outlet/ViewOutlet"),
    meta: {
      title: "View Outlet",
      i18n: "outlet_view",
      authCode: "org:outlet:query",
    },
  },

  {
    path: "/add/retail/product",
    component: () => import("@p/RetailMainData/Product/AddProduct"),
    meta: {
      title: "Product",
      i18n: "product_add",
      authCode: "system:product:save",
    },
  },

  {
    path: "/view/retail/product",
    component: () => import("@p/RetailMainData/Product/ViewProduct"),
    meta: {
      title: "Product",
      i18n: "product_view",
      authCode: "system:product:query",
    },
  },
];

export default retailRoute;
