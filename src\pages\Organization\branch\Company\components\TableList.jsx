import React, { useMemo } from "react";
import { useDispatch } from "react-redux";
import { useDispatchClient } from "@/hooks/client";
import { useNavigate } from "react-router-dom";
import ZktecoTable from "@c/ZktecoTable";
import { useTranslation } from "react-i18next";
import { useConfirm } from "@/components/zkconfirm";
import { toast } from "react-toastify";
import { deltenant } from "@s/api/tenant";
function TableList({
  data,
  isLoading,
  isRefetching,
  isError,
  rowCount,
  pagination,
  setPagination,
  setOpen,
  setTenantId,
  getTableData,
}) {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { stateSetClientId, stateSetClientCode } = useDispatchClient();
  const navigate = useNavigate();
  const confirmFn = useConfirm();
  const columns = useMemo(
    () => [
      {
        accessorKey: "name",
        header: t("branch.name"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "areaName",
        header: t("branch.region"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "email",
        header: t("branch.email"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "monile",
        header: t("branch.mobile"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ row }) => {
          const countryCode = row.original.countryCode || "";
          const phone = row.original.phone || "";
          return `+${countryCode} ${phone}`.trim();
        },
      },
    ],
    []
  );

  // 删除
  const handlerDelete = async (data) => {
    confirmFn({
      title: t("common.common_delete_confirm"),
      confirmationText: t("common.common_confirm"),
      cancellationText: t("table.cancel"),
      description: t("common.common_confirm_delete"),
    }).then(() => {
      deltenant(data?.id).then((res) => {
        toast.success(res.message);
        getTableData();
      });
    });
  };

  const isShowAction = {
    isShowView: "org:branch:query",
    isShowEditor: "org:branch:update",
    isShowUserSetting: true,
    isShowDetele: "org:branch:delete",
  };

  const handlePageChange = useCallback(
    (pageIndex) => {
      setPagination((prev) => ({ ...prev, pageIndex }));
    },
    [setPagination]
  );

  const handlePageSizeChange = useCallback(
    (pageSize) => {
      setPagination({ pageIndex: 0, pageSize });
    },
    [setPagination]
  );

  const actionHandlers = useMemo(
    () => ({
      handlerView: (data) =>
        navigate("/org/branch/view", { state: { id: data?.id, type: "view" } }),
      handlerEditor: (data) =>
        navigate("/org/branch/edit", {
          state: { id: data?.id, type: "editor" },
        }),
      handlerUserSetting: (data) => {
        dispatch(stateSetClientId(data?.id));
        dispatch(stateSetClientCode(data?.tenantCode));
        navigate("/org/branch/employee/list", {
          state: { id: data?.id, tenantCode: data?.tenantCode },
        });
      },
      Detele: (data) => {
        handlerDelete(data);
      },
    }),
    [
      dispatch,
      navigate,
      setOpen,
      setTenantId,
      stateSetClientId,
      stateSetClientCode,
    ]
  );

  return (
    <ZktecoTable
      columns={columns}
      data={data}
      rowCount={rowCount}
      isLoading={isLoading}
      isRefetching={isRefetching}
      isError={isError}
      pathRoute="/org/branch/edit"
      loadDada={getTableData}
      paginationProps={{
        currentPage: pagination.pageIndex,
        rowsPerPage: pagination.pageSize,
        onPageChange: handlePageChange,
        onPageSizeChange: handlePageSizeChange,
      }}
      topActions={{
        showAdd: "org:branch:save",
      }}
      actionHandlers={actionHandlers}
      isShowAction={isShowAction}
    />
  );
}

export default TableList;
