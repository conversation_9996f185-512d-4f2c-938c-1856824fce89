const partnerRoutes = [
   {
    path: "/org/partner",
    component: () => import("@p/Organization/partner/company/index"),
    meta: {
      title: "Partner",
      i18n: "parter_list",
      authCode: "org:partner:list",
    },
  },
  {
    path: "/org/partner/add",
    component: () =>
      import("@p/Organization/partner/company/components/AddPartner"),
    meta: {
      title: "Add Partner",
      i18n: "parter_add",
      authCode: "org:partner:save",
    },
  },

  {
    path: "/org/partner/view",
    component: () =>
      import("@p/Organization/partner/company/components/ViewPartner"),
    meta: {
      title: "View Partner",
      i18n: "parter_view",
      authCode: "org:partner:query",
    },
  },

  {
    path: "/org/partner/employee/list",
    component: () => import("@p/Organization/partner/Employee/index"),
    meta: {
      title: "Employee List",
      i18n: "parter_user_list",
      authCode: "org:partner_employee:list",
    },
  },

  {
    path: "/org/partner/employee/add",
    component: () =>
      import("@p/Organization/partner/Employee/components/AddEmployee"),
    meta: {
      title: "Add Employee",
      i18n: "parter_user_add",
      authCode: "org:partner_employee:save",
    },
  },

  {
    path: "/org/partner/employee/view",
    component: () =>
      import("@p/Organization/partner/Employee/components/ViewEmployee"),
    meta: {
      title: "View Employee",
      i18n: "parter_user_view",
      authCode: "org:partner_employee:query",
    },
  }
];

export default partnerRoutes;
