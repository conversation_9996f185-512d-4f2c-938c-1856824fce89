const microRoutes = [
  {
    path: "/cms-app/*",
    component: () => import("@/components/MicroAppContainer"),
    props: { appName: "cms-app", title: "CMS应用" },
    meta: {
      title: "CMS应用",
      needLogin: true,
      isMicroApp: true,
    },
  },
  {
    path: "/retail-ai-app/*",
    component: () => import("@/components/MicroAppContainer"),
    props: { appName: "retail-ai-app", title: "零售AI应用" },
    meta: {
      title: "零售AI应用",
      needLogin: true,
      isMicroApp: true,
    },
  },
  {
    path: "/e-price-tag-app/*",
    component: () => import("@/components/MicroAppContainer"),
    props: { appName: "e-price-tag-app", title: "电子价签应用" },
    meta: {
      title: "电子价签应用",
      needLogin: true,
      isMicroApp: true,
    },
  },
];


export default microRoutes;