/*
 * @Description: 主组件
 */
import React, { useMemo } from "react";
import { useRoutes } from "react-router-dom";
import Fn from "./fn";

function RouterWaiter({ routes, onRouteBefore, loading }) {
  const fn = useMemo(
    () =>
      new Fn({
        routes,
        onRouteBefore,
        loading,
      }),
    [routes, onRouteBefore, loading]
  );

  const reactRoutes = useMemo(() => fn.transformRoutes(), [fn]);
  const elements = useRoutes(reactRoutes);

  return elements;
}

export default RouterWaiter;
