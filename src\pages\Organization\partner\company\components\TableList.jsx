import React from "react";
import ZktecoTable from "@c/ZktecoTable";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { useDispatchClient } from "@/hooks/client.js";
import { toast } from "react-toastify";
import { deleteParner } from "@s/api/partner";
import { useConfirm } from "@/components/zkconfirm";
function TenantList({
  data,
  isLoading,
  isRefetching,
  isError,
  rowCount,
  pagination,
  setPagination,
  setOpen,
  setTenantId,
  getTableData,
}) {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const confirmFn = useConfirm();
  const { stateSetClientId, stateSetClientCode } = useDispatchClient();

  const columns = useMemo(
    () => [
      {
        accessorKey: "photo",
        header: t("partner.logo"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ row }) => (
          <Avatar
            src={row.original.photo}
            alt={"Logo"}
            size="medium"
            sx={{
              width: "121px",
              height: "50px",
              borderRadius: "8px",
              border: "1px solid #E3E3E3",
            }}
          />
        ),
      },
      {
        accessorKey: "name",
        header: t("partner.partner_name"),
        enableColumnActions: false,
        enableClickToCopy: true, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "phone",
        header: t("ips.ips_store_phone"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },

      {
        accessorKey: "areaName",
        header: t("partner_user.address"),
        enableColumnActions: false,
        enableSorting: false,
        enableClickToCopy: false, // 启用点击复制
      },
      {
        accessorKey: "email",
        header: t("partner_user.email"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
    ],
    []
  );

  const isShowAction = {
    isShowView: "org:partner:list",
    isShowEditor: "org:partner:update",
    isShowUserSetting: true,
    isShowDetele: "org:principal:delete",
    isShowSendEamil: false,
  };

  const handlePageChange = useCallback(
    (pageIndex) => {
      setPagination((prev) => ({ ...prev, pageIndex }));
    },
    [setPagination]
  );

  const handlePageSizeChange = useCallback(
    (pageSize) => {
      setPagination({ pageIndex: 0, pageSize });
    },
    [setPagination]
  );

  // 删除
  const handlerDetele = async (data) => {
    confirmFn({
      title: t("common.common_delete_confirm"),
      confirmationText: t("common.common_confirm"),
      cancellationText: t("table.cancel"),
      description: t("branch.delete_content"),
    }).then(() => {
      deleteParner(data?.id).then((res) => {
        toast.success(res.message);
        getTableData();
      });
    });
  };

  const actionHandlers = useMemo(
    () => ({
      handlerView: (data) =>
        navigate("/org/partner/view", { state: { id: data?.id, type: "view" } }),
      handlerEditor: (data) =>
        navigate("/org/partner/add", {
          state: { id: data?.id, type: "editor" },
        }),
      handlerUserSetting: (data) => {
        dispatch(stateSetClientId(data?.id));
        dispatch(stateSetClientCode(data?.code));
        navigate("/org/partner/employee/list", {
          state: { id: data?.id, departmentId: data?.departmentId },
        });
      },
      Detele: (data) => {
        handlerDetele(data);
      },
    }),
    [
      dispatch,
      navigate,
      setOpen,
      setTenantId,
      stateSetClientId,
      stateSetClientCode,
    ]
  );

  return (
    <ZktecoTable
      columns={columns}
      data={data}
      rowCount={rowCount}
      isLoading={isLoading}
      isRefetching={isRefetching}
      isError={isError}
      pathRoute="/org/partner/add"
      loadDada={getTableData}
      paginationProps={{
        currentPage: pagination.pageIndex,
        rowsPerPage: pagination.pageSize,
        onPageChange: handlePageChange,
        onPageSizeChange: handlePageSizeChange,
      }}
      topActions={{
        showAdd: "org:partner:save",
      }}
      actionHandlers={actionHandlers}
      isShowAction={isShowAction}
    />
  );
}

export default TenantList;
