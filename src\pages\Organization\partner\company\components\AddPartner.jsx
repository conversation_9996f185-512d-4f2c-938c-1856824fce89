import React from "react";
import RightViewLayout from "@c/layoutComponent/RightViewLayout";
import { useTranslation } from "react-i18next";
import { useFormik } from "formik";
import UploadImage from "@c/UploadImage";
import ZkFormik from "@c/Config/ZkFormik.jsx";
import { addParner, editParner, getParnerDetail } from "@s/api/partner";
import { useNavigate, useLocation } from "react-router-dom";
import { getFormConfig } from "./FormConfig";
import { toast } from "react-toastify";
import { createValidation } from "@c/Config/validationUtils.js";
function AddParner(props) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const treeSelectRef = React.useRef(null);
  const { state } = useLocation();
  const [imageUrl, setImageUrl] = useState(null);
  const [fileUrl, setFileUrl] = useState(null);
  const [loading, setLoading] = React.useState(false);
  const [formConfig, setFormConfig] = useState([]);
  const [detailData, setDetailData] = useState([]);
  const handleUpload = (file) => {
    setFileUrl(file);
    const reader = new FileReader();
    reader.onload = (e) => {
      setImageUrl(e.target.result);
    };
    reader.readAsDataURL(file);
  };

  useEffect(() => {
    const formConfig = getFormConfig(t, state?.type, treeSelectRef);
    setFormConfig(formConfig);

    if (state?.type === "editor" || state?.type === "view") {
      getParnerDetail(state?.id).then((res) => {
        setImageUrl(res?.data?.photo);
        setDetailData(res?.data);
        treeSelectRef.current.setItem({
          id: res?.data?.areaId,
          name: res?.data?.areaName,
        });
      });
    }
  }, []);

  let initialValues = {};
  if (state?.type !== "editor") {
    initialValues = {
      name: "",
      email: "",
      countryCode: "",
      phone: "",
      areaName: "",
      //  password: "",
    };
  } else {
    initialValues = {
      id: detailData?.id,
      name: detailData?.name,
      email: detailData?.email,
      countryCode: detailData?.countryCode,
      phone: detailData?.phone,
      areaId: detailData?.areaId,
      areaName: detailData?.areaName,
    };
  }

  const formik = useFormik({
    initialValues: initialValues,
    validationSchema: createValidation(formConfig),
    enableReinitialize: true, // 允许重新初始化
    onSubmit: (values) => {
      let params = {
        ...values,
        multipartFile: fileUrl,
      };
      setLoading(true);
      if (state?.type === "editor") {
        editParner(params)
          .then((response) => {
            toast.success(response?.message);
            navigate("/org/partner");
          })
          .finally(() => {
            setLoading(false);
          });
      } else {
        addParner(params)
          .then((response) => {
            toast.success(response?.message);
            navigate("/org/partner");
          })
          .finally(() => {
            setLoading(false);
          });
      }
    },
  });

  return (
    <React.Fragment>
      <RightViewLayout
        navigateBack={"/org/partner"}
        title={
          state?.type === "editor"
            ? t("partner.edit_partner")
            : t("partner.add_partner")
        }
        handleSubmit={formik.handleSubmit}
        handleCancle={() => {
          navigate("/org/partner");
        }}
        loading={loading}>
        <Grid container mt={2} mb={2}>
          <UploadImage
            label={t("common.common_company_logo")}
            imageUrl={imageUrl}
            setImageUrl={setImageUrl}
            handleUpload={(file) => {
              handleUpload(file, setImageUrl, setFileUrl);
            }}></UploadImage>
        </Grid>

        <ZkFormik sx={6} formik={formik} formConfig={formConfig}></ZkFormik>
      </RightViewLayout>
    </React.Fragment>
  );
}

export default AddParner;
