import React, {
  useState,
  useEffect,
  useImperativeHandle,
  forwardRef,
} from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  TextField,
  Button,
  Box,
  Stack,
  IconButton,
  Alert,
  CircularProgress,
  Divider,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import CloseIcon from "@mui/icons-material/Close";
import SecurityIcon from "@mui/icons-material/Security";
import EmailIcon from "@mui/icons-material/Email";
import VerifiedUserIcon from "@mui/icons-material/VerifiedUser";
import { useTranslation } from "react-i18next";
import { toast } from "react-toastify";

// 自定义样式的Dialog
const StyledDialog = styled(Dialog)(({ theme }) => ({
  "& .MuiDialog-paper": {
    borderRadius: "16px",
    padding: theme.spacing(1),
    maxWidth: "480px",
    width: "100%",
    margin: theme.spacing(2),
  },
}));

// 自定义样式的DialogTitle
const StyledDialogTitle = styled(DialogTitle)(({ theme }) => ({
  padding: theme.spacing(3, 3, 1, 3),
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
  "& .MuiTypography-root": {
    fontWeight: 600,
    fontSize: "1.25rem",
    color: theme.palette.text.primary,
  },
}));

// 验证码输入框容器
const CodeInputContainer = styled(Box)(({ theme }) => ({
  display: "flex",
  gap: theme.spacing(1),
  justifyContent: "center",
  marginTop: theme.spacing(2),
  marginBottom: theme.spacing(2),
}));

// 单个验证码输入框
const CodeInput = styled(TextField)(({ theme }) => ({
  "& .MuiOutlinedInput-root": {
    width: "48px",
    height: "48px",
    "& input": {
      textAlign: "center",
      fontSize: "1.25rem",
      fontWeight: "bold",
      padding: 0,
    },
    "&.Mui-focused": {
      "& .MuiOutlinedInput-notchedOutline": {
        borderColor: theme.palette.primary.main,
        borderWidth: "2px",
      },
    },
  },
}));

// 信息卡片样式
const InfoCard = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.grey[50],
  borderRadius: "12px",
  padding: theme.spacing(2),
  marginBottom: theme.spacing(2),
  border: `1px solid ${theme.palette.grey[200]}`,
}));

const TwoFactorAuthDialog = forwardRef(
  (
    {
      open,
      onClose,
      onVerify,
      email,
      loading = false,
      onResendCode,
      clearCodeOnError = false, // 新增：验证失败时是否清空验证码
      onError, // 新增：验证失败时的回调
    },
    ref
  ) => {
    const { t } = useTranslation();
    const [code, setCode] = useState(["", "", "", "", "", ""]);
    const [countdown, setCountdown] = useState(0);
    const [error, setError] = useState("");
    const [isResending, setIsResending] = useState(false);

    // 清空验证码的方法
    const clearCode = () => {
      setCode(["", "", "", "", "", ""]);
      setError("");
      // 聚焦到第一个输入框
      setTimeout(() => {
        const firstInput = document.getElementById("code-input-0");
        if (firstInput) firstInput.focus();
      }, 100);
    };

    // 倒计时效果
    useEffect(() => {
      let timer;
      if (countdown > 0) {
        timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      }
      return () => clearTimeout(timer);
    }, [countdown]);

    // 暴露给父组件的方法
    useImperativeHandle(ref, () => ({
      clearCode: () => {
        clearCode();
      },

      handleVerificationError: (errorMessage) => {
        handleVerificationError(errorMessage);
      },
    }));

    // 处理验证码输入
    const handleCodeChange = (index, value) => {
      if (value.length > 1) return; // 只允许输入一个字符

      const newCode = [...code];
      newCode[index] = value;
      setCode(newCode);
      setError(""); // 清除错误信息

      // 自动跳转到下一个输入框
      if (value && index < 5) {
        const nextInput = document.getElementById(`code-input-${index + 1}`);
        if (nextInput) nextInput.focus();
      }
    };

    // 处理键盘事件
    const handleKeyDown = (index, event) => {
      if (event.key === "Backspace" && !code[index] && index > 0) {
        const prevInput = document.getElementById(`code-input-${index - 1}`);
        if (prevInput) prevInput.focus();
      }
      if (event.key === "Enter" && code.every((c) => c)) {
        handleVerify();
      }
    };

    // 处理粘贴事件
    const handlePaste = (event) => {
      event.preventDefault();
      const pastedData = event.clipboardData.getData("text").slice(0, 6);
      const newCode = pastedData
        .split("")
        .concat(Array(6).fill(""))
        .slice(0, 6);
      setCode(newCode);
      setError("");
    };

    // 验证验证码
    const handleVerify = async () => {
      const verificationCode = code.join("");
      if (verificationCode.length !== 6) {
        setError(t("two_factor_auth.enter_complete_code"));
        return;
      }

      try {
        await onVerify(verificationCode);
      } catch (error) {
        // 验证失败时清空验证码

        // 调用错误回调
        if (onError) {
          onError(error);
        }
        // 设置错误信息
        setError(error.message || t("two_factor_auth.verification_failed"));
      }
    };

    // 提供给外部调用的验证失败处理方法
    const handleVerificationError = (errorMessage) => {
      setError(errorMessage || t("two_factor_auth.verification_failed"));
    };

    // 重新发送验证码
    const handleResendCode = async () => {
      if (countdown > 0) return;

      setIsResending(true);
      try {
        await onResendCode();
        setCountdown(60); // 60秒倒计时
        setCode(["", "", "", "", "", ""]);
        setError("");
        // toast.success(t("two_factor_auth.code_resent_success"));
      } finally {
        setIsResending(false);
      }
    };

    // 关闭弹窗时重置状态
    const handleClose = () => {
      setCode(["", "", "", "", "", ""]);
      setError("");
      setCountdown(0);
      onClose();
    };

    return (
      <StyledDialog
        open={open}
        onClose={handleClose}
        maxWidth="sm"
        fullWidth
        disableEscapeKeyDown={loading}>
        <StyledDialogTitle>
          <Box display="flex" alignItems="center" gap={1}>
            <SecurityIcon color="primary" />
            <Typography>{t("two_factor_auth.title")}</Typography>
          </Box>
          <IconButton
            onClick={handleClose}
            disabled={loading}
            size="small"
            sx={{ color: "grey.500" }}>
            <CloseIcon />
          </IconButton>
        </StyledDialogTitle>

        <DialogContent sx={{ px: 3, pb: 2 }}>
          {/* 双因子认证说明 */}
          <InfoCard>
            <Box display="flex" alignItems="flex-start" gap={2}>
              <VerifiedUserIcon color="primary" sx={{ mt: 0.5 }} />
              <Box>
                <Typography variant="subtitle2" fontWeight="600" gutterBottom>
                  {t("two_factor_auth.what_is_2fa")}
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ lineHeight: 1.6 }}>
                  {t("two_factor_auth.description")}
                </Typography>
              </Box>
            </Box>
          </InfoCard>

          <Divider sx={{ my: 2 }} />

          {/* 邮箱信息 */}
          <Box display="flex" alignItems="center" gap={1} mb={2}>
            <EmailIcon color="action" />
            <Typography variant="body2" color="text.secondary">
              {t("two_factor_auth.code_sent_to")}
            </Typography>
            <Typography variant="body2" fontWeight="600">
              {email}
            </Typography>
          </Box>

          {/* 验证码输入 */}
          <Typography
            variant="body1"
            fontWeight="600"
            textAlign="center"
            mb={1}>
            {t("two_factor_auth.enter_code")}
          </Typography>

          <CodeInputContainer>
            {code.map((digit, index) => (
              <CodeInput
                key={index}
                id={`code-input-${index}`}
                value={digit}
                onChange={(e) => handleCodeChange(index, e.target.value)}
                onKeyDown={(e) => handleKeyDown(index, e)}
                onPaste={index === 0 ? handlePaste : undefined}
                inputProps={{
                  maxLength: 1,
                  pattern: "[0-9]*",
                  inputMode: "numeric",
                }}
                disabled={loading}
                error={!!error}
              />
            ))}
          </CodeInputContainer>

          {/* 错误信息 */}
          {/* {error && (
            <Alert severity="error" sx={{ mt: 1, mb: 2 }}>
              {error}
            </Alert>
          )} */}

          {/* 重新发送验证码 */}
          <Box textAlign="center" mt={2}>
            <Typography variant="body2" color="text.secondary" mb={1}>
              {t("two_factor_auth.no_code_received")}
            </Typography>
            <Button
              variant="text"
              onClick={handleResendCode}
              disabled={countdown > 0 || isResending || loading}
              startIcon={isResending ? <CircularProgress size={16} /> : null}>
              {countdown > 0
                ? t("two_factor_auth.resend_countdown", { count: countdown })
                : isResending
                ? t("two_factor_auth.sending")
                : t("two_factor_auth.resend_code")}
            </Button>
          </Box>
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 3, pt: 1 }}>
          <Stack direction="row" spacing={2} width="100%">
            <Button
              variant="outlined"
              onClick={handleClose}
              disabled={loading}
              fullWidth
              sx={{ height: 48 }}>
              {t("two_factor_auth.cancel")}
            </Button>
            <Button
              variant="contained"
              onClick={handleVerify}
              disabled={loading || code.some((c) => !c)}
              fullWidth
              sx={{
                height: 48,
                background: "linear-gradient(90deg, #78BC27, #1487CB)",
                "&:hover": {
                  background: "linear-gradient(90deg, #6BA322, #1276B8)",
                },
              }}
              startIcon={
                loading ? <CircularProgress size={20} color="inherit" /> : null
              }>
              {loading
                ? t("two_factor_auth.verifying")
                : t("two_factor_auth.verify")}
            </Button>
          </Stack>
        </DialogActions>
      </StyledDialog>
    );
  }
);

// 设置组件显示名称
TwoFactorAuthDialog.displayName = "TwoFactorAuthDialog";

export default TwoFactorAuthDialog;
