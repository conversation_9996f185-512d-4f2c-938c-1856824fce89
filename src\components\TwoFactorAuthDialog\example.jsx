import React, { useState } from "react";
import TwoFactorAuthDialog from "./index";
import { Button, Box, Typography } from "@mui/material";
import { toast } from "react-toastify";

/**
 * 双因子验证弹窗使用示例
 * 
 * 这个示例展示了如何在登录流程中集成双因子验证功能
 */
const TwoFactorAuthExample = () => {
  const [showTwoFactorDialog, setShowTwoFactorDialog] = useState(false);
  const [loading, setLoading] = useState(false);
  const userEmail = "<EMAIL>"; // 用户邮箱

  // 模拟登录成功后触发双因子验证
  const handleLoginSuccess = () => {
    // 假设登录成功，服务器返回需要双因子验证
    setShowTwoFactorDialog(true);
  };

  // 处理验证码验证
  const handleVerifyCode = async (verificationCode) => {
    setLoading(true);
    try {
      // 模拟API调用验证验证码
      console.log("验证码:", verificationCode);
      
      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 模拟验证成功
      if (verificationCode === "123456") {
        toast.success("验证成功！正在跳转...");
        setShowTwoFactorDialog(false);
        // 这里可以跳转到主页面
        console.log("跳转到主页面");
      } else {
        toast.error("验证码错误，请重新输入");
      }
    } catch (error) {
      toast.error("验证失败，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  // 处理重新发送验证码
  const handleResendCode = async () => {
    // 模拟API调用发送验证码
    console.log("重新发送验证码到:", userEmail);
    
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 这里应该调用实际的发送验证码API
    console.log("验证码已发送");
  };

  // 关闭弹窗
  const handleCloseDialog = () => {
    setShowTwoFactorDialog(false);
  };

  return (
    <Box sx={{ p: 4, textAlign: "center" }}>
      <Typography variant="h4" gutterBottom>
        双因子验证弹窗示例
      </Typography>
      
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        点击下面的按钮来模拟登录成功后的双因子验证流程
      </Typography>

      <Button
        variant="contained"
        size="large"
        onClick={handleLoginSuccess}
        sx={{
          background: "linear-gradient(90deg, #78BC27, #1487CB)",
          "&:hover": {
            background: "linear-gradient(90deg, #6BA322, #1276B8)",
          }
        }}
      >
        模拟登录成功 (触发双因子验证)
      </Button>

      <Box sx={{ mt: 4, p: 2, bgcolor: "grey.100", borderRadius: 2 }}>
        <Typography variant="subtitle2" gutterBottom>
          测试提示：
        </Typography>
        <Typography variant="body2" color="text.secondary">
          • 输入验证码 "123456" 可以模拟验证成功<br/>
          • 输入其他验证码会显示错误信息<br/>
          • 可以测试重新发送验证码功能<br/>
          • 支持键盘导航和粘贴功能
        </Typography>
      </Box>

      {/* 双因子验证弹窗 */}
      <TwoFactorAuthDialog
        open={showTwoFactorDialog}
        onClose={handleCloseDialog}
        onVerify={handleVerifyCode}
        email={userEmail}
        loading={loading}
        onResendCode={handleResendCode}
      />
    </Box>
  );
};

export default TwoFactorAuthExample;

/**
 * 在实际的登录页面中的集成示例：
 * 
 * 1. 在Login.jsx中导入组件：
 * import TwoFactorAuthDialog from "@/components/TwoFactorAuthDialog";
 * 
 * 2. 添加状态管理：
 * const [showTwoFactorDialog, setShowTwoFactorDialog] = useState(false);
 * const [twoFactorLoading, setTwoFactorLoading] = useState(false);
 * 
 * 3. 修改登录成功的处理逻辑：
 * // 在 handleQuickLoginSubmit 函数中
 * userLogin(params)
 *   .then((res) => {
 *     if (res?.code == "00000000") {
 *       // 检查是否需要双因子验证
 *       if (res.data.requireTwoFactor) {
 *         setShowTwoFactorDialog(true);
 *         return;
 *       }
 *       
 *       // 正常登录流程
 *       setToken(res.data.access_token);
 *       // ... 其他登录逻辑
 *     }
 *   })
 * 
 * 4. 添加验证码验证函数：
 * const handleVerifyTwoFactor = async (verificationCode) => {
 *   setTwoFactorLoading(true);
 *   try {
 *     const response = await verifyTwoFactorCode({
 *       email: quickFormik.values.username,
 *       code: verificationCode
 *     });
 *     
 *     if (response.code === "00000000") {
 *       setToken(response.data.access_token);
 *       setShowTwoFactorDialog(false);
 *       loginCallback();
 *     }
 *   } catch (error) {
 *     toast.error("验证失败");
 *   } finally {
 *     setTwoFactorLoading(false);
 *   }
 * };
 * 
 * 5. 在JSX中添加弹窗组件：
 * <TwoFactorAuthDialog
 *   open={showTwoFactorDialog}
 *   onClose={() => setShowTwoFactorDialog(false)}
 *   onVerify={handleVerifyTwoFactor}
 *   email={quickFormik.values.username}
 *   loading={twoFactorLoading}
 *   onResendCode={handleResendTwoFactorCode}
 * />
 */
