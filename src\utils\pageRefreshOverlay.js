/**
 * 页面刷新遮罩工具
 * 用于在页面刷新时显示持久的加载遮罩，确保用户体验的连续性
 */

class PageRefreshOverlay {
  constructor() {
    this.overlay = null;
    this.isActive = false;
  }

  /**
   * 创建页面刷新遮罩
   */
  createOverlay() {
    if (this.overlay) {
      return this.overlay;
    }

    this.overlay = document.createElement('div');
    this.overlay.id = 'page-refresh-overlay';
    this.overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.98);
      backdrop-filter: blur(12px);
      z-index: 10001;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      opacity: 0;
      transition: opacity 0.3s ease-in-out;
    `;

    // 创建加载内容
    const content = document.createElement('div');
    content.style.cssText = `
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
    `;

    // 创建加载图标
    const spinner = document.createElement('div');
    spinner.style.cssText = `
      width: 60px;
      height: 60px;
      border: 4px solid #e3f2fd;
      border-top: 4px solid #1976d2;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 24px;
    `;

    // 创建消息文本
    const message = document.createElement('div');
    message.style.cssText = `
      font-size: 18px;
      font-weight: 500;
      color: #333;
      margin-bottom: 8px;
    `;
    message.textContent = '正在刷新页面...';

    // 创建副标题
    const subtitle = document.createElement('div');
    subtitle.style.cssText = `
      font-size: 14px;
      color: #666;
      margin-bottom: 24px;
    `;
    subtitle.textContent = '请稍候，正在为您准备最佳体验';

    // 创建进度条容器
    const progressContainer = document.createElement('div');
    progressContainer.style.cssText = `
      width: 300px;
      height: 6px;
      background: rgba(0, 0, 0, 0.1);
      border-radius: 3px;
      overflow: hidden;
      margin-bottom: 12px;
    `;

    // 创建进度条
    const progressBar = document.createElement('div');
    progressBar.style.cssText = `
      height: 100%;
      background: #1976d2;
      border-radius: 3px;
      width: 0%;
      transition: width 0.3s ease;
    `;

    // 创建进度文本
    const progressText = document.createElement('div');
    progressText.style.cssText = `
      font-size: 12px;
      color: #888;
    `;
    progressText.textContent = '0%';

    // 组装元素
    progressContainer.appendChild(progressBar);
    content.appendChild(spinner);
    content.appendChild(message);
    content.appendChild(subtitle);
    content.appendChild(progressContainer);
    content.appendChild(progressText);
    this.overlay.appendChild(content);

    // 添加CSS动画
    const style = document.createElement('style');
    style.textContent = `
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(style);

    // 存储引用以便后续操作
    this.progressBar = progressBar;
    this.progressText = progressText;
    this.messageElement = message;

    return this.overlay;
  }

  /**
   * 显示页面刷新遮罩
   */
  show() {
    if (this.isActive) {
      return;
    }

    console.log('🎭 显示页面刷新遮罩');
    
    const overlay = this.createOverlay();
    document.body.appendChild(overlay);
    
    // 强制重排，然后显示
    overlay.offsetHeight;
    overlay.style.opacity = '1';
    
    this.isActive = true;
    
    // 开始进度动画
    this.startProgressAnimation();
  }

  /**
   * 隐藏页面刷新遮罩
   */
  hide() {
    if (!this.isActive || !this.overlay) {
      return;
    }

    console.log('🎭 隐藏页面刷新遮罩');
    
    this.overlay.style.opacity = '0';
    
    setTimeout(() => {
      if (this.overlay && this.overlay.parentNode) {
        this.overlay.parentNode.removeChild(this.overlay);
      }
      this.isActive = false;
    }, 300);
  }

  /**
   * 开始进度动画
   */
  startProgressAnimation() {
    if (!this.progressBar || !this.progressText) {
      return;
    }

    let progress = 0;
    const steps = [
      { progress: 15, message: '正在保存当前状态...', delay: 200 },
      { progress: 30, message: '正在清理缓存...', delay: 300 },
      { progress: 45, message: '正在重新加载资源...', delay: 400 },
      { progress: 60, message: '正在初始化应用...', delay: 500 },
      { progress: 75, message: '正在恢复状态...', delay: 400 },
      { progress: 90, message: '正在完成最后步骤...', delay: 300 },
    ];

    let currentStep = 0;

    const executeStep = () => {
      if (currentStep < steps.length && this.isActive) {
        const step = steps[currentStep];
        
        this.progressBar.style.width = `${step.progress}%`;
        this.progressText.textContent = `${step.progress}%`;
        this.messageElement.textContent = step.message;
        
        currentStep++;
        setTimeout(executeStep, step.delay);
      }
    };

    executeStep();
  }

  /**
   * 更新进度
   */
  updateProgress(progress, message) {
    if (this.progressBar && this.progressText) {
      this.progressBar.style.width = `${progress}%`;
      this.progressText.textContent = `${progress}%`;
    }
    
    if (message && this.messageElement) {
      this.messageElement.textContent = message;
    }
  }

  /**
   * 检查是否处于活动状态
   */
  isVisible() {
    return this.isActive;
  }
}

// 创建全局实例
const pageRefreshOverlay = new PageRefreshOverlay();

// 在开发环境下暴露到全局
if (import.meta.env.DEV) {
  window.pageRefreshOverlay = pageRefreshOverlay;
}

export default pageRefreshOverlay;
