/**
 * 子应用路由跳转示例
 * 展示如何在子应用中使用优化的导航方法
 */

import React, { useState, useEffect } from 'react';

// 示例：在子应用的组件中使用优化的导航方法
const SubAppNavigationExample = (props) => {
  const [currentRoute, setCurrentRoute] = useState(null);
  const [navigationHistory, setNavigationHistory] = useState([]);
  
  // 从 props 中获取导航辅助工具
  const { navigationHelper } = props;

  useEffect(() => {
    // 获取当前路由信息
    if (navigationHelper) {
      const route = navigationHelper.getCurrentRoute();
      setCurrentRoute(route);
    }

    // 监听路由变化事件
    const handleRouteChange = (event) => {
      const { appName, from, to, timestamp } = event.detail;
      setNavigationHistory(prev => [...prev, { from, to, timestamp }]);
    };

    const handleBrowserNavigation = (event) => {
      const { appName, path, timestamp } = event.detail;
      if (navigationHelper) {
        const route = navigationHelper.getCurrentRoute();
        setCurrentRoute(route);
      }
    };

    window.addEventListener('microapp-internal-navigation', handleRouteChange);
    window.addEventListener('microapp-browser-navigation', handleBrowserNavigation);

    return () => {
      window.removeEventListener('microapp-internal-navigation', handleRouteChange);
      window.removeEventListener('microapp-browser-navigation', handleBrowserNavigation);
    };
  }, [navigationHelper]);

  // 使用优化的导航方法
  const handleNavigateToList = () => {
    if (navigationHelper) {
      const success = navigationHelper.navigateWithinMicroApp('/cms-app/list', {
        replace: false,
        state: { from: 'dashboard' },
        onSuccess: ({ from, to }) => {
          console.log(`✅ 导航成功: ${from} -> ${to}`);
          alert('成功跳转到列表页！');
        },
        onError: (error) => {
          console.error('❌ 导航失败:', error);
          alert('导航失败: ' + error.message);
        }
      });

      if (!success) {
        alert('导航失败，请检查路径是否正确');
      }
    }
  };

  const handleNavigateToDetail = () => {
    if (navigationHelper) {
      navigationHelper.navigateWithinMicroApp('/cms-app/detail/123', {
        replace: false,
        state: { id: 123, from: 'list' },
        onSuccess: ({ from, to }) => {
          console.log(`✅ 导航成功: ${from} -> ${to}`);
          alert('成功跳转到详情页！');
        },
        onError: (error) => {
          console.error('❌ 导航失败:', error);
          alert('导航失败: ' + error.message);
        }
      });
    }
  };

  const handleNavigateWithReplace = () => {
    if (navigationHelper) {
      navigationHelper.navigateWithinMicroApp('/cms-app/settings', {
        replace: true, // 替换当前历史记录
        onSuccess: ({ from, to }) => {
          console.log(`✅ 替换导航成功: ${from} -> ${to}`);
          alert('成功替换到设置页！');
        },
        onError: (error) => {
          console.error('❌ 导航失败:', error);
          alert('导航失败: ' + error.message);
        }
      });
    }
  };

  // 尝试跨应用跳转（这会失败）
  const handleCrossAppNavigation = () => {
    if (navigationHelper) {
      navigationHelper.navigateWithinMicroApp('/retail-ai-app/dashboard', {
        onSuccess: ({ from, to }) => {
          console.log(`✅ 导航成功: ${from} -> ${to}`);
        },
        onError: (error) => {
          console.error('❌ 导航失败:', error);
          alert('跨应用导航失败: ' + error.message);
        }
      });
    }
  };

  // 使用全局方法的示例
  const handleGlobalNavigation = () => {
    if (window.microAppNavigation) {
      window.microAppNavigation.navigate('/cms-app/reports', {
        onSuccess: ({ from, to }) => {
          console.log(`✅ 全局导航成功: ${from} -> ${to}`);
          alert('使用全局方法成功跳转到报告页！');
        },
        onError: (error) => {
          console.error('❌ 全局导航失败:', error);
          alert('全局导航失败: ' + error.message);
        }
      });
    } else {
      alert('全局导航方法不可用');
    }
  };

  // 检查路径有效性
  const handleCheckPath = () => {
    if (navigationHelper) {
      const validPaths = [
        '/cms-app/list',
        '/cms-app/detail/123',
        '/retail-ai-app/dashboard', // 这个对当前应用无效
        '/invalid-path'
      ];

      validPaths.forEach(path => {
        const isValid = navigationHelper.isValidPath(path);
        console.log(`路径 ${path} 是否有效: ${isValid}`);
      });

      alert('路径检查结果已输出到控制台');
    }
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h2>子应用路由跳转示例</h2>
      
      {/* 当前路由信息 */}
      <div style={{ marginBottom: '20px', padding: '10px', backgroundColor: '#f5f5f5' }}>
        <h3>当前路由信息</h3>
        {currentRoute ? (
          <div>
            <p><strong>路径:</strong> {currentRoute.path}</p>
            <p><strong>应用名称:</strong> {currentRoute.appName}</p>
            <p><strong>是否有效:</strong> {currentRoute.isValid ? '是' : '否'}</p>
            <p><strong>时间戳:</strong> {new Date(currentRoute.timestamp).toLocaleString()}</p>
          </div>
        ) : (
          <p>无法获取路由信息</p>
        )}
      </div>

      {/* 导航按钮 */}
      <div style={{ marginBottom: '20px' }}>
        <h3>导航操作</h3>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <button onClick={handleNavigateToList} style={buttonStyle}>
            跳转到列表页
          </button>
          <button onClick={handleNavigateToDetail} style={buttonStyle}>
            跳转到详情页
          </button>
          <button onClick={handleNavigateWithReplace} style={buttonStyle}>
            替换到设置页
          </button>
          <button onClick={handleCrossAppNavigation} style={buttonStyle}>
            尝试跨应用跳转（会失败）
          </button>
          <button onClick={handleGlobalNavigation} style={buttonStyle}>
            使用全局方法跳转
          </button>
          <button onClick={handleCheckPath} style={buttonStyle}>
            检查路径有效性
          </button>
        </div>
      </div>

      {/* 导航历史 */}
      <div>
        <h3>导航历史</h3>
        {navigationHistory.length > 0 ? (
          <ul style={{ maxHeight: '200px', overflowY: 'auto' }}>
            {navigationHistory.map((item, index) => (
              <li key={index} style={{ marginBottom: '5px' }}>
                <strong>{new Date(item.timestamp).toLocaleTimeString()}</strong>: 
                {item.from} → {item.to}
              </li>
            ))}
          </ul>
        ) : (
          <p>暂无导航历史</p>
        )}
      </div>
    </div>
  );
};

// 按钮样式
const buttonStyle = {
  padding: '8px 16px',
  margin: '4px',
  backgroundColor: '#007bff',
  color: 'white',
  border: 'none',
  borderRadius: '4px',
  cursor: 'pointer',
  fontSize: '14px'
};

export default SubAppNavigationExample;

// 使用 Hook 风格的示例组件
export const HookStyleNavigationExample = (props) => {
  const { navigationHelper } = props;
  
  // 使用 Hook 风格的导航方法
  const { navigate, getCurrentRoute, isValidPath } = navigationHelper?.useNavigation() || {};

  const handleNavigate = (path) => {
    if (navigate) {
      const success = navigate(path, {
        onSuccess: ({ from, to }) => {
          console.log(`Hook 导航成功: ${from} -> ${to}`);
        },
        onError: (error) => {
          console.error('Hook 导航失败:', error);
        }
      });

      if (!success) {
        alert('Hook 导航失败');
      }
    }
  };

  return (
    <div style={{ padding: '20px' }}>
      <h3>Hook 风格导航示例</h3>
      <button 
        onClick={() => handleNavigate('/cms-app/hook-example')}
        style={buttonStyle}
      >
        使用 Hook 导航
      </button>
    </div>
  );
};
