const principalRoutes = [
  {
    path: "/org/principal",
    component: () => import("@p/Organization/Principal/Company/index"),
    meta: {
      title: "Principal",
      i18n: "principal_list",
      authCode: ["org:principal:list","org:principal:save"],
    },
  },
  {
    path: "/org/principal/add",
    component: () =>
      import("@p/Organization/Principal/Company/components/AddPrincipal"),
    meta: {
      title: "Add Principal",
      i18n: "principal_add",
      authCode: "org:principal:save",
    },
  },
  {
    path: "/org/principal/view",
    component: () =>
      import("@p/Organization/Principal/Company/components/ViewPrincipal"),
    meta: {
      title: "View Principal",
      i18n: "principal_view",
      authCode: "org:principal:query",
    },
  },
  {
    path: "/org/principal/employee/list",
    component: () => import("@p/Organization/Principal/Employee/index"),
    meta: {
      title: "Employee List",
      i18n: "principal_user_list",
      authCode: "org:principal_employee:list",
    },
  },

  {
    path: "/org/principal/employee/add",
    component: () =>
      import("@p/Organization/Principal/Employee/components/AddEmployee"),
    meta: {
      title: "Add Employee",
      i18n: "principal_user_add",
      authCode: "org:principal_employee:save",
    },
  },

  {
    path: "/org/principal/employee/view",
    component: () =>
      import("@p/Organization/Principal/Employee/components/ViewEmployee"),
    meta: {
      title: "View Employee",
      i18n: "principal_user_view",
      authCode: "org:principal_employee:query",
    },
  },

  
];

export default principalRoutes;
